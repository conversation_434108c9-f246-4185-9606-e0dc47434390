# MongoDB Configuration
MONGODB_CONNECTION_STRING=mongodb://localhost:27017
MONGODB_DATABASE=agno_sales_agent

# API Keys
MONDAY_API_TOKEN=your_monday_api_token_here
TAVILY_API_KEY=your_tavily_api_key_here
GOOGLE_API_KEY=your_google_gemini_api_key_here

# Monday.com Configuration
MONDAY_BOARD_ID=your_board_id_here

# WhatsApp Configuration
WHATSAPP_SESSION_PATH=./whatsapp/.wwebjs_auth

# Backend Configuration
BACKEND_HOST=localhost
BACKEND_PORT=8000
DEBUG=true
ENVIRONMENT=development

# Chrome Extension Configuration
EXTENSION_ID=your_extension_id_here

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/agno_sales_agent.log
