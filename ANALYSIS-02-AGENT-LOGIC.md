# Analysis 02: Agent Logic & Data Flow

This document provides a detailed analysis of each AI agent's specific implementation, data flow, and interaction with MongoDB. It builds upon the high-level overview in `ANALYSIS-01-ARCHITECTURE.md` by examining the source code of each component.

## Core Workflow Agents

These agents form the primary pipeline for processing a lead from initial trigger to final outreach.

### 1. `WorkflowCoordinator`

-   **File:** `backend/agents/workflow_coordinator.py`
-   **Primary Responsibility:** Acts as the central orchestrator or "manager" for the entire lead processing pipeline. It ensures each lead passes through the defined stages in the correct order: Research -> Message Generation -> Outreach.
-   **Data Input:**
    -   Consumes a `WorkflowInput` object, which contains the initial, comprehensive lead data extracted from Monday.com (e.g., `lead_id`, `name`, `company`, `phone_number`).
-   **Data Output:**
    -   Produces a `WorkflowResult` object, which is a final summary of the entire process, including success status, key performance metrics like `research_confidence` and `message_personalization_score`, and the final `outreach_status`.
-   **MongoDB Interaction:**
    -   **Reads `agent_configurations`:** Fetches the configuration for all other agents to initialize them. This allows for dynamic tuning of the entire system from a single MongoDB document.
    -   **Reads `contacts` collection:** Retrieves the full, enriched contact document for a given `lead_id`. This document serves as the "single source of truth" passed to other agents.
    -   **Writes to `workflow_progress`:** Creates and updates a document in this collection to log the real-time status of the workflow for a given lead. This is essential for monitoring and debugging.
-   **Dependencies:**
    -   Directly invokes `ResearchAgent`, `MessageGenerationAgent`, and `OutreachAgent`.
    -   Relies on `ResearchStorageManager` to persist and retrieve research data, ensuring data continuity.

### 2. `ResearchAgent`

-   **File:** `backend/agents/research_agent.py`
-   **Primary Responsibility:** Gathers external intelligence on a lead and their company. It uses the Tavily API to perform advanced web searches and synthesizes the findings into actionable insights.
-   **Data Input:**
    -   The primary method, `research_lead_enhanced`, consumes a `LeadInput` object (basic lead details) and, critically, the full `crm_data` dictionary from the lead's document in the `contacts` collection. This allows the research to be guided by existing CRM context.
-   **Data Output:**
    -   Produces a `ResearchOutput` object. The schema for this is extensive, containing nested dictionaries for `company_intelligence`, `decision_maker_insights`, and a specific `mongodb_opportunity` assessment. It also generates `conversation_hooks` and a `timing_rationale`.
-   **MongoDB Interaction:**
    -   This agent is a consumer of data prepared by the `WorkflowCoordinator`. It reads the `agent_configurations` document (passed during initialization) to get its prompts and settings.
    -   The `WorkflowCoordinator` is responsible for persisting the agent's output (`ResearchOutput`) into the `research_results` collection via the `ResearchStorageManager`.
-   **Dependencies:**
    -   Called by the `WorkflowCoordinator`.
    -   Relies on the Tavily Search API for external data gathering.

### 3. `MessageGenerationAgent`

-   **File:** `backend/agents/message_agent.py`
-   **Primary Responsibility:** Generates a hyper-personalized outreach message using Google's Gemini model. Its core function is to synthesize all available data into a compelling, context-aware message.
-   **Data Input:**
    -   The `generate_hyper_personalized_message` method consumes a `MessageInput` object. Most importantly, it takes the `crm_data` (from the `contacts` collection) and `enhanced_research` (from the `research_results` collection) as arguments. This provides the agent with the complete, up-to-date context for the lead.
-   **Data Output:**
    -   Produces a `MessageOutput` object, which contains the final `message_text`, a `personalization_score`, and a `predicted_response_rate`.
-   **MongoDB Interaction:**
    -   Like the `ResearchAgent`, it is a consumer of data orchestrated by the `WorkflowCoordinator`. It does not query MongoDB directly but relies on the data passed to it, which originates from the `contacts`, `research_results`, and `agent_configurations` collections.
-   **Dependencies:**
    -   Called by the `WorkflowCoordinator`.
    -   Relies on the Google Gemini API for message generation.

### 4. `OutreachAgent`

-   **File:** `backend/agents/outreach_agent.py`
-   **Primary Responsibility:** Manages the final step of the workflow: sending the generated message and updating external systems.
-   **Data Input:**
    -   Consumes an `OutreachRequest` object. A critical field is `pre_generated_message`, which is the text generated by the `MessageGenerationAgent`.
-   **Data Output:**
    -   Produces an `OutreachResult` object, which records the outcome of the sending attempt, including the `whatsapp_message_id` and final `status`.
-   **MongoDB Interaction:**
    -   Its actions trigger updates in other systems, which are then logged back into MongoDB by supporting agents.
    -   It uses the `StatusTrackingSystem` to record the interaction history.
    -   It uses the `OutreachErrorRecoverySystem` which, in case of failure, writes to the `message_queue` collection.
-   **Dependencies:**
    -   Called by the `WorkflowCoordinator`.
    -   `WhatsAppBridge`: A Node.js service for sending messages.
    -   `MondayStatusUpdater`: A client to update the lead's status on the Monday.com board.
    -   `StatusTrackingSystem` and `OutreachErrorRecoverySystem`.

---

## Supporting & State Management Agents

These agents are not part of the sequential workflow but are critical for data persistence, state management, and resilience.

### 1. `ResearchStorageManager`

-   **File:** `backend/agents/research_storage.py`
-   **Primary Responsibility:** A dedicated service for abstracting all interactions with research-related MongoDB collections. It enforces the "single source of truth" principle for research data.
-   **MongoDB Interaction:**
    -   **`research_results` collection:** It performs all `CRUD` (Create, Read, Update, Delete) operations on this collection. The `store_research_result` method is used to save the output from the `ResearchAgent`.
    -   **`research_agent_sessions` collection:** Manages the storage of agent conversation history for debugging and traceability, using the `Agno` library's built-in `MongoDbStorage`.
-   **Data Schema:** The `ResearchRecord` dataclass defines the schema for documents stored in the `research_results` collection.

### 2. `StatusTrackingSystem`

-   **File:** `backend/agents/status_tracking_system.py`
-   **Primary Responsibility:** Tracks the lifecycle of a sent message after the initial outreach is complete (e.g., delivered, read).
-   **MongoDB Interaction:**
    -   **`interaction_history` collection:** Its primary function is to write to this collection. It creates a new `InteractionRecord` document for every status change (e.g., `MESSAGE_DELIVERED`, `MESSAGE_READ`), creating a comprehensive audit trail for each lead.
-   **Data Schema:** The `InteractionRecord` dataclass defines the schema for the `interaction_history` collection.

### 3. `OutreachErrorRecoverySystem`

-   **File:** `backend/agents/outreach_error_recovery.py`
-   **Primary Responsibility:** Provides resilience to the outreach process. It catches errors (e.g., WhatsApp disconnection), implements retry logic, and queues messages that cannot be sent immediately.
-   **MongoDB Interaction:**
    -   **`message_queue` collection:** This is the core of the recovery system. When an outreach fails with a retryable error, the system writes a `QueuedMessage` document to this collection. A background process monitors the connection and re-attempts to send messages from this queue when the connection is restored.
-   **Data Schema:** The `QueuedMessage` and `ErrorRecord` dataclasses define the schema for the documents it stores.

---

## Experimental / Ancillary Agents

These agents exist in the codebase but do not appear to be part of the primary, orchestrated workflow.

-   **`MessageQualityOptimizer`** (`backend/agents/message_quality_optimizer.py`): An agent designed to assess and improve the quality of generated messages to meet specific targets (e.g., >40% response rate). It introduces a message approval workflow (`PENDING`, `APPROVED`, `REJECTED`).
-   **`MultimodalMessageAgent`** (`backend/agents/multimodal_message_agent.py`): An advanced agent capable of generating voice (via OpenAI) and image (via Gemini) content in addition to text. It saves generated media to a local `tmp/` directory.