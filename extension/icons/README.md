# Extension Icons

This directory should contain the following icon files for the Chrome extension:

## Required Icons

- `icon16.png` - 16x16 pixels (toolbar icon)
- `icon32.png` - 32x32 pixels (Windows computers often require this size)
- `icon48.png` - 48x48 pixels (extension management page)
- `icon128.png` - 128x128 pixels (Chrome Web Store and installation)

## Icon Design Guidelines

- **Theme**: AI/Robot theme with Monday.com integration
- **Colors**: Use the Agno brand colors (#667eea, #764ba2)
- **Style**: Modern, clean, professional
- **Content**: Robot/AI symbol (🤖) or similar tech-forward design

## Temporary Solution

For development and testing, you can:

1. Create simple colored squares with the Agno gradient
2. Use online icon generators
3. Use the 🤖 emoji as a base design
4. Convert SVG to PNG at required sizes

## Production Icons

For production deployment, create professional icons that:
- Represent AI-powered sales automation
- Are recognizable at small sizes
- Follow Chrome extension design guidelines
- Match the Monday.com aesthetic when possible

## Tools for Icon Creation

- Figma (free)
- Canva (free tier)
- GIMP (free)
- Adobe Illustrator (paid)
- Online favicon generators
