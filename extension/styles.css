/* styles.css - Chrome Extension Content Script Styles */
/* Styles for Monday.com integration and Agno Sales Agent UI elements */

/* Agno Process Button Styles */
.agno-process-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border: none !important;
    padding: 8px 16px !important;
    border-radius: 6px !important;
    font-size: 12px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    margin-left: 8px !important;
    transition: all 0.3s ease !important;
    z-index: 9999 !important;
    position: relative !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    line-height: 1.2 !important;
    text-align: center !important;
    white-space: nowrap !important;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2) !important;
}

.agno-process-btn:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
}

.agno-process-btn:active {
    transform: scale(0.98) !important;
    box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3) !important;
}

.agno-process-btn:disabled {
    cursor: not-allowed !important;
    opacity: 0.7 !important;
    transform: none !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Processing State Styles */
.agno-process-btn.processing {
    background: linear-gradient(135deg, #ffa500 0%, #ff8c00 100%) !important;
    cursor: not-allowed !important;
}

.agno-process-btn.success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

.agno-process-btn.error {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
}

/* Loading Animation */
.agno-loading {
    display: inline-block !important;
    width: 12px !important;
    height: 12px !important;
    border: 2px solid transparent !important;
    border-top: 2px solid white !important;
    border-radius: 50% !important;
    animation: agno-spin 1s linear infinite !important;
    margin-right: 6px !important;
    vertical-align: middle !important;
}

@keyframes agno-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Agno Status Indicator */
.agno-status-indicator {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    background: white !important;
    border: 2px solid #667eea !important;
    border-radius: 8px !important;
    padding: 12px 16px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    z-index: 50000000 !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    color: #333 !important;
    max-width: 300px !important;
    opacity: 0 !important;
    transform: translateY(-20px) !important;
    transition: all 0.3s ease !important;
}

.agno-status-indicator.show {
    opacity: 1 !important;
    transform: translateY(0) !important;
}

.agno-status-indicator.success {
    border-color: #28a745 !important;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%) !important;
    color: #155724 !important;
}

.agno-status-indicator.error {
    border-color: #dc3545 !important;
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%) !important;
    color: #721c24 !important;
}

.agno-status-indicator.processing {
    border-color: #ffc107 !important;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
    color: #856404 !important;
}

/* Agno Progress Bar */
.agno-progress-container {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 4px !important;
    background: rgba(102, 126, 234, 0.1) !important;
    z-index: 10001 !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
}

.agno-progress-container.show {
    opacity: 1 !important;
}

.agno-progress-bar {
    height: 100% !important;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%) !important;
    width: 0% !important;
    transition: width 0.3s ease !important;
    border-radius: 0 2px 2px 0 !important;
}

/* Agno Tooltip */
.agno-tooltip {
    position: absolute !important;
    background: #333 !important;
    color: white !important;
    padding: 8px 12px !important;
    border-radius: 6px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    white-space: nowrap !important;
    z-index: 10002 !important;
    opacity: 0 !important;
    transform: translateY(-10px) !important;
    transition: all 0.2s ease !important;
    pointer-events: none !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.agno-tooltip.show {
    opacity: 1 !important;
    transform: translateY(0) !important;
}

.agno-tooltip::after {
    content: '' !important;
    position: absolute !important;
    top: 100% !important;
    left: 50% !important;
    margin-left: -5px !important;
    border-width: 5px !important;
    border-style: solid !important;
    border-color: #333 transparent transparent transparent !important;
}

/* Monday.com Integration Specific Styles */
.agno-monday-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.5) !important;
    z-index: 9998 !important;
    display: none !important;
    align-items: center !important;
    justify-content: center !important;
}

.agno-monday-modal {
    background: white !important;
    border-radius: 12px !important;
    padding: 24px !important;
    max-width: 500px !important;
    width: 90% !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2) !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.agno-monday-modal h3 {
    margin: 0 0 16px 0 !important;
    color: #333 !important;
    font-size: 18px !important;
    font-weight: 700 !important;
}

.agno-monday-modal p {
    margin: 0 0 20px 0 !important;
    color: #666 !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
}

.agno-monday-modal .buttons {
    display: flex !important;
    gap: 12px !important;
    justify-content: flex-end !important;
}

.agno-monday-modal button {
    padding: 10px 20px !important;
    border: none !important;
    border-radius: 6px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
}

.agno-monday-modal .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
}

.agno-monday-modal .btn-secondary {
    background: #f8f9fa !important;
    color: #495057 !important;
    border: 1px solid #dee2e6 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .agno-process-btn {
        padding: 6px 12px !important;
        font-size: 11px !important;
    }
    
    .agno-status-indicator {
        top: 10px !important;
        right: 10px !important;
        padding: 10px 12px !important;
        font-size: 12px !important;
        max-width: 250px !important;
    }
    
    .agno-monday-modal {
        padding: 20px !important;
        width: 95% !important;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .agno-process-btn {
        border: 2px solid white !important;
    }
    
    .agno-status-indicator {
        border-width: 3px !important;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .agno-process-btn,
    .agno-status-indicator,
    .agno-progress-container,
    .agno-tooltip {
        transition: none !important;
    }
    
    .agno-loading {
        animation: none !important;
    }
    
    .agno-process-btn:hover {
        transform: none !important;
    }
}
