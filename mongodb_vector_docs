<div id="std-label-vector-search-quick-start"></div>

# Atlas Vector Search Quick Start

This quick start describes how to load sample documents that contain vector embeddings into an Atlas cluster or local Atlas deployment, create an Atlas Vector Search index on those embeddings, and then perform semantic search to return documents that are similar to your query.

*Time required: 15 minutes*

<Tabs>

<Tab name="Atlas UI">

</Tab>

<Tab name="MongoDB Shell">

</Tab>

<Tab name="C">

</Tab>

<Tab name="C++11">

</Tab>

<Tab name="C#">

</Tab>

<Tab name="Go">

</Tab>

<Tab name="Java (Sync)">

</Tab>

<Tab name="Kotlin (Coroutine)">

</Tab>

<Tab name="Kotl<PERSON> (Sync)">

</Tab>

<Tab name="Node.js">

</Tab>

<Tab name="PHP">

</Tab>

<Tab name="Python">

Work with a runnable version of this tutorial as a [Python notebook](https://github.com/mongodb/docs-notebooks/blob/main/get-started/quick-start.ipynb?tck=docs).

</Tab>

<Tab name="Ruby">

</Tab>

<Tab name="Rust">

</Tab>

<Tab name="Scala">

</Tab>

</Tabs>

## Objectives

In this quick start, you complete the following steps:

1. Create an index definition for the `sample_mflix.embedded_movies` collection that indexes the `plot_embedding` field as the `vector` type. The `plot_embedding` field contains embeddings created using OpenAI's `text-embedding-ada-002` embedding model. The index definition specifies `1536` vector dimensions and measures similarity using `dotProduct`.

2. Run an Atlas Vector Search query that searches the sample `sample_mflix.embedded_movies` collection. The query uses the [`$vectorSearch`](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-stage/#mongodb-pipeline-pipe.-vectorSearch) stage to search the `plot_embedding` field, which contains embeddings created using OpenAI's `text-embedding-ada-002` embedding model. The query searches the `plot_embedding` field using vector embeddings for the string *time travel*. It considers up to `150` nearest neighbors, and returns `10` documents in the results.

To learn more, see [Learning Summary](https://mongodb.com/docs/atlas/atlas-vector-search/tutorials/vector-search-quick-start/#std-label-avs-qs-learning-summary).

## Create a Vector Search Index

➤➤ To set the client you use to run the examples on this page, use the **Select your language** drop-down menu in the right navigation pane.

create[Supported Clients](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-index-supported-drivers)Vector Search`vector_index``sample_mflix` database`embedded_movies` collection`sample_mflix.embedded_movies``plot_embedding`Dot ProductScalarIn this section, you create an Atlas Vector Search index on sample data that you load into an Atlas cluster or a deployment hosted on your local computer:

<Tabs>

<Tab name="Atlas Cluster">

### Set up your Atlas cluster.

- [Create a free Atlas account or sign in to an existing account](https://account.mongodb.com/account/register?tck=docs_atlas).

- If you don't yet have an Atlas cluster, [create a free M0 cluster](https://cloud.mongodb.com/go?l=https%3A%2F%2Fcloud.mongodb.com%2Fv2%2F%3Cproject%3E%23clusters%2Fedit%3Ffrom%3DctaClusterHeader). To learn more about creating an Atlas cluster, see [Create a Cluster](https://mongodb.com/docs/atlas/tutorial/create-new-cluster/#std-label-create-new-cluster).

  If you are working with an existing cluster, you must have [`Project Data Access Admin`](https://mongodb.com/docs/atlas/reference/user-roles/#mongodb-authrole-Project-Data-Access-Admin) or higher [access](https://mongodb.com/docs/atlas/access/manage-project-access/#std-label-who-can-access-project) to your Atlas project.

  If you create a new cluster, you have the necessary permissions by default.

  You can create only one `M0` Free cluster per [project.](https://mongodb.com/docs/atlas/atlas-ui-authorization/#std-label-atlas-ui-auth-projects)

- If you haven't yet loaded the [sample dataset](https://mongodb.com/docs/atlas/atlas-vector-search/tutorials/vector-search-quick-start/#std-label-vector-search-quickstart-sample-data) for this quick start onto your cluster, [load](https://mongodb.com/docs/atlas/sample-data/#std-label-load-sample-data) the `sample_mflix` sample database onto your cluster.

  If you already loaded the `sample_mflix` dataset, [check](https://mongodb.com/docs/atlas/atlas-ui/collections/#std-label-atlas-ui-collections) that the `sample_mflix` database contains the `embedded_movies` collection. If it doesn't, [drop](https://mongodb.com/docs/atlas/atlas-ui/databases/#std-label-atlas-ui-drop-a-db) the `sample_mflix` database and [reload](https://mongodb.com/docs/atlas/sample-data/#std-label-load-sample-data) the `sample_mflix` dataset.

  Loading the sample dataset can take several minutes to complete.

- In the left sidebar, click [Atlas Search](https://cloud.mongodb.com/go?l=https%3A%2F%2Fcloud.mongodb.com%2Fv2%2F%3Cproject%3E%23%2Fclusters%2FatlasSearch). Choose your cluster from the Select data source menu and click Go to Atlas Search.

### Create a Vector Search index.

<Tabs>

<Tab name="Atlas UI">

- When the sample data finishes loading, click Create Search Index.

- Make the following selections on the page and then click Next.

  `sample_mflix``embedded_movies`Atlas Vector Search<table>

  <tr>
  <td>
  Search Type

  </td>
  <td>
  Select the Atlas Vector Search index type.

  </td>
  </tr>
  <tr>
  <td>
  Index Name and Data Source

  </td>
  <td>
  Specify the following information:

  - Index Name: `vector_index`

  - Database and Collection:

    - `sample_mflix`

    - `embedded_movies`

  </td>
  </tr>
  <tr>
  <td>
  Configuration Method

  </td>
  <td>
  For a guided experience, select Visual Editor.To edit the raw index definition, select JSON Editor.

  </td>
  </tr>
  </table>

- Define the index.

  <Tabs>

  <Tab name="Visual Editor">

  Atlas automatically detects fields that contain vector embeddings, as well as their corresponding dimensions. For the `sample_mflix.embedded_movies` collection, the `plot_embedding` field displays.

  To configure the index, do the following:

  - Select Dot Product from the Similarity Method dropdown.

  - Click the Advanced dropdown menu and select Scalar to [automatically quantize](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization) the embeddings in the field.

  </Tab>

  <Tab name="JSON Editor">

  Copy and paste the following [vector search index definition](https://mongodb.com/docs/atlas/atlas-vector-search/tutorials/vector-search-quick-start/#std-label-vector-search-quickstart-vector-index-definition) into the JSON Editor.

  ```
  {
      "fields": [{
      "type": "vector",
      "path": "plot_embedding",
      "numDimensions": 1536,
      "similarity": "dotProduct"
      "quantization": "scalar"
      }]
  }
  ```

  </Tab>

  </Tabs>

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

- Click Next.

- Click Create Vector Search Index.

  The index should take about one minute to build. When your vector index is finished building, the Status column reads Active.

</Tab>

<Tab name="MongoDB Shell">

- Connect to the Atlas cluster using [`mongosh`](https://www.mongodb.com/docs/mongodb-shell/#mongodb-binary-bin.mongosh).

  Open [`mongosh`](https://www.mongodb.com/docs/mongodb-shell/#mongodb-binary-bin.mongosh) in a terminal window and connect to your Atlas cluster. For detailed instructions on connecting, see [Connect via mongosh](https://mongodb.com/docs/atlas/mongo-shell-connection/#std-label-connect-mongo-shell).

- Switch to the database that contains the collection for which you want to create the index.

  ```shell
  use sample_mflix
  ```

  ```shell
  switched to db sample_mflix
  ```

- Run the [`db.collection.createSearchIndex()`](https://www.mongodb.com/docs/manual/reference/method/db.collection.createSearchIndex/#mongodb-method-db.collection.createSearchIndex) method.

  ```shell
  db.embedded_movies.createSearchIndex(
    "vector_index",
    "vectorSearch",
    {
      "fields": [
        {
          "type": "vector",
          "path": "plot_embedding",
          "numDimensions": 1536,
          "similarity": "dotProduct",
          "quantization": "scalar"
        }
      ]
    }
  );
  ```

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

</Tab>

<Tab name="C">

- Install the MongoDB C Driver.

  For detailed installation instructions, refer to the [MongoDB C Driver documentation](https://www.mongodb.com/docs/languages/c/c-driver/current/libmongoc/tutorials/obtaining-libraries/).

- Create a new directory called `query-quick-start`.

  ```bash
  mkdir query-quick-start
  ```

- Enter the directory, and create a `CMakeLists.txt` file.

  ```bash
  cd query-quick-start
  touch CMakeLists.txt
  ```

  Copy and paste the following lines into the `CMakeLists.txt` file:

  ```console
  cmake_minimum_required(VERSION 3.30)

  project(atlas-vector-search-quick-start)

  # Specify the minimum version for creating a vector index.
  find_package (mongoc-1.0 1.28.0 REQUIRED)

  add_executable(atlas-vector-search-quick-start
    vector_index.c
  )

  target_link_libraries (atlas-vector-search-quick-start PRIVATE mongo::mongoc_shared)
  ```

- Define the index.

  Create a file named `vector_index.c`. Copy and paste the following code into the file.

  ```c
  #include <bson/bson.h>
  #include <mongoc/mongoc.h>
  #include <stdio.h>

  int main(void) {
    mongoc_client_t *client;
    mongoc_collection_t *collection;
    bson_error_t error;
    char database_name[] = "sample_mflix";
    char collection_name[] = "embedded_movies";
    char index_name[] = "vector_index";

    mongoc_init();

    // Replace the placeholder with your Atlas connection string
    client =  mongoc_client_new("<connection-string>");

    // Connect to your Atlas cluster
    collection = mongoc_client_get_collection (client, database_name, collection_name);

    bson_t cmd;
    // Create search index command.
    {
      char *cmd_str = bson_strdup_printf (
          BSON_STR ({
                      "createSearchIndexes" : "%s",
                      "indexes" : [{
                        "definition": {
                          "fields": [{
                            "type": "vector",
                            "path": "plot_embedding",
                            "numDimensions": 1536,
                            "similarity": "dotProduct",
                            "quantization": "scalar"
                          }]
                        },
                        "name": "%s",
                        "type": "vectorSearch"
                      }]
                    }),
          collection_name, index_name);
      if (!bson_init_from_json(&cmd, cmd_str, -1, &error)) {
        printf("Failed to initialize BSON: %s\n", error.message);
        bson_free(cmd_str);
        return 1;
      }
      bson_free (cmd_str);
    }
    if (!mongoc_collection_command_simple (collection, &cmd, NULL /* read_prefs */, NULL /* reply */, &error)) {
      bson_destroy (&cmd);
      printf ("Failed to run createSearchIndexes: %s", error.message);
      return 1;
    } else {
      printf ("New search index named %s is building.\n", index_name);
      bson_destroy (&cmd);
    }

    // Polling for index status
    printf("Polling to check if the index is ready. This may take up to a minute.\n");
    int queryable = 0;
    while (!queryable) {
      const char *pipeline_str = "{\"pipeline\": [{\"$listSearchIndexes\": {}}]}";
      bson_t pipeline;
      if (!bson_init_from_json(&pipeline, pipeline_str, -1, &error)) {
        printf("Failed to initialize pipeline BSON: %s\n", error.message);
        break; // Exit the loop on error
      }
      mongoc_cursor_t *cursor = mongoc_collection_aggregate(collection, MONGOC_QUERY_NONE, &pipeline, NULL, NULL);
      const bson_t *got;
      // Check if the cursor returns any documents
      int found_index = 0;
      while (mongoc_cursor_next(cursor, &got)) {
        bson_iter_t iter;
        if (bson_iter_init(&iter, got) && bson_iter_find(&iter, "name")) {
          const char *name = bson_iter_utf8(&iter, NULL);
          if (strcmp(name, index_name) == 0) {
            found_index = 1; // Index found
            bson_iter_find(&iter, "queryable");
            queryable = bson_iter_bool(&iter);
            break; // Exit the loop since we found the index
          }
        }
      }
      if (mongoc_cursor_error(cursor, &error)) {
        printf("Failed to run $listSearchIndexes: %s\n", error.message);
        break; // Exit the loop on error
      }
      if (!found_index) {
        printf("Index %s not found yet. Retrying...\n", index_name);
      }
      bson_destroy(&pipeline);
      mongoc_cursor_destroy(cursor);
      sleep(5); // Sleep for 5 seconds before checking again
    }
    if (queryable) {
      printf("%s is ready for querying.\n", index_name);
    } else {
      printf("Error occurred or index not found.\n");
    }

    // Cleanup
    mongoc_collection_destroy(collection);
    mongoc_client_destroy(client);
    mongoc_cleanup();

    return 0;
  }
  ```

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

  This code also includes a polling mechanism to check if the index is ready to use.

- Specify the `<connection-string>`.

  <Tabs>

  <Tab name="Atlas Cluster">

  Your connection string should use the following format:

  ```
  mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
  ```

  Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

  </Tab>

  <Tab name="Local Deployment">

  Your connection string should use the following format:

  ```
  mongodb://localhost:<port-number>/?directConnection=true
  ```

  </Tab>

  </Tabs>

- Create and enter the `/build` directory:

  ```bash
  mkdir build && cd build
  ```

- Prepare the project.

  ```bash
  cmake ../
  ```

- Build the app.

  ```bash
  cmake --build .
  ```

- Execute the app to create the index.

  ```bash
  ./atlas-vector-search-quick-start
  ```

  ```sh
  New search index named vector_index is building.
  Polling to check if the index is ready. This may take up to a minute.
  vector_index is ready for querying.

  ```

</Tab>

<Tab name="C++11">

- Install the MongoDB C++ Driver.

  For detailed installation instructions, refer to the [MongoDB C++ Driver documentation](https://www.mongodb.com/docs/languages/cpp/cpp-driver/current/installation/).

- Create a new directory called `query-quick-start`.

  ```bash
  mkdir query-quick-start
  ```

- Enter the directory, and create a `CMakeLists.txt` file.

  ```bash
  cd query-quick-start
  touch CMakeLists.txt
  ```

  Copy and paste the following lines into the `CMakeLists.txt` file:

  ```console
  cmake_minimum_required(VERSION 3.30)

  project(query_quick_start)

  set(CMAKE_CXX_STANDARD 17)

  # Specify the minimum version for creating a vector index.
  find_package(mongocxx 3.11.0 REQUIRED)
  find_package(bsoncxx REQUIRED)

  add_executable(query_quick_start
    vector_index.cpp
  )

  target_link_libraries(query_quick_start PRIVATE mongo::mongocxx_shared)
  target_link_libraries(query_quick_start PRIVATE mongo::bsoncxx_shared)
  ```

- Define the index.

  Create a file named `vector_index.cpp`. Copy and paste the following code into the file.

  ```cpp
  #include <bsoncxx/builder/basic/document.hpp>
  #include <iostream>
  #include <mongocxx/client.hpp>
  #include <mongocxx/instance.hpp>
  #include <mongocxx/search_index_view.hpp>
  #include <mongocxx/uri.hpp>
  #include <thread>

  using bsoncxx::builder::basic::kvp;
  using bsoncxx::builder::basic::make_array;
  using bsoncxx::builder::basic::make_document;

  int main() {
    try {
      mongocxx::instance inst{};

      // Replace the placeholder with your Atlas connection string
      const auto uri = mongocxx::uri{"<connection-string>"};

      // Connect to your Atlas cluster
      mongocxx::client conn{uri};
      auto db = conn["sample_mflix"];
      auto collection = db["embedded_movies"];

      auto siv = collection.search_indexes();
      std::string name = "vector_index";
      auto type = "vectorSearch";
      auto definition = make_document(
          kvp("fields",
              make_array(make_document(
                  kvp("type", "vector"), kvp("path", "plot_embedding"),
                  kvp("numDimensions", 1536), kvp("similarity", "dotProduct"),
                  kvp("quantization", "scalar")))));
      auto model =
          mongocxx::search_index_model(name, definition.view()).type(type);
      siv.create_one(model);
      std::cout << "New search index named " << name << " is building."
                << std::endl;

      // Polling for index status
      std::cout << "Polling to check if the index is ready. This may take up to "
                   "a minute."
                << std::endl;
      bool queryable = false;
      while (!queryable) {
        auto indexes = siv.list();
        for (const auto& index : indexes) {
          if (index["name"].get_value() == name) {
            queryable = index["queryable"].get_bool();
          }
        }
        if (!queryable) {
          std::this_thread::sleep_for(std::chrono::seconds(5));
        }
      }
      std::cout << name << " is ready for querying." << std::endl;
    } catch (const std::exception& e) {
      std::cout << "Exception: " << e.what() << std::endl;
    }
    return 0;
  }

  ```

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

  This code also includes a polling mechanism to check if the index is ready to use.

- Specify the `<connection-string>`.

  <Tabs>

  <Tab name="Atlas Cluster">

  Your connection string should use the following format:

  ```
  mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
  ```

  Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

  </Tab>

  <Tab name="Local Deployment">

  Your connection string should use the following format:

  ```
  mongodb://localhost:<port-number>/?directConnection=true
  ```

  </Tab>

  </Tabs>

- Create and enter the `/build` directory:

  ```bash
  mkdir build && cd build
  ```

- Prepare the project.

  ```bash
  cmake ../
  ```

- Build the app.

  ```bash
  cmake --build .
  ```

- Execute the app to create the index.

  ```bash
  ./query_quick_start
  ```

  ```sh
  New search index named vector_index is building.
  Polling to check if the index is ready. This may take up to a minute.
  vector_index is ready for querying.

  ```

</Tab>

<Tab name="C#">

- called `query-quick-start` and initialize your .NET/C# project.

  ```bash
  mkdir query-quick-start
  cd query-quick-start
  dotnet new console
  ```

- Run the following command to add the .NET/C# Driver to your project as a dependency.

  ```bash
  dotnet add package MongoDB.Driver
  ```

  For more detailed installation instructions, see the [MongoDB C# Driver documentation](https://www.mongodb.com/docs/drivers/csharp/current/quick-start/#std-label-csharp-quickstart).

- Define the index.

  Create a file named `IndexService.cs`. Copy and paste the following code into the file.

  ```csharp
  namespace query_quick_start;

  using MongoDB.Bson;
  using MongoDB.Driver;
  using System;
  using System.Threading;

  public class IndexService
  {
      // Replace the placeholder with your Atlas connection string
      private const string MongoConnectionString = "<connection-string>";
      public void CreateVectorIndex()
      {
          try
          {
              // Connect to your Atlas cluster
              var client = new MongoClient(MongoConnectionString);
              var database = client.GetDatabase("sample_mflix");
              var collection = database.GetCollection<BsonDocument>("embedded_movies");

              var searchIndexView = collection.SearchIndexes;
              var name = "vector_index";
              var type = SearchIndexType.VectorSearch;

              var definition = new BsonDocument
              {
                  { "fields", new BsonArray
                      {
                          new BsonDocument
                          {
                              { "type", "vector" },
                              { "path", "plot_embedding" },
                              { "numDimensions", 1536 },
                              { "similarity", "dotProduct" },
                              { "quantization", "scalar" }
                          }
                      }
                  }
              };

              var model = new CreateSearchIndexModel(name, type, definition);

              searchIndexView.CreateOne(model);
              Console.WriteLine($"New search index named {name} is building.");

              // Polling for index status
              Console.WriteLine("Polling to check if the index is ready. This may take up to a minute.");
              bool queryable = false;
              while (!queryable)
              {
                  var indexes = searchIndexView.List();
                  foreach (var index in indexes.ToEnumerable())
                  {
                      if (index["name"] == name)
                      {
                          queryable = index["queryable"].AsBoolean;
                      }
                  }
                  if (!queryable)
                  {
                      Thread.Sleep(5000);
                  }
              }
              Console.WriteLine($"{name} is ready for querying.");
          }
          catch (Exception e)
          {
              Console.WriteLine($"Exception: {e.Message}");
          }
      }
  }

  ```

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

  This code also includes a polling mechanism to check if the index is ready to use.

- Specify the `<connection-string>`.

  <Tabs>

  <Tab name="Atlas Cluster">

  Your connection string should use the following format:

  ```
  mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
  ```

  Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

  </Tab>

  <Tab name="Local Deployment">

  Your connection string should use the following format:

  ```
  mongodb://localhost:<port-number>/?directConnection=true
  ```

  </Tab>

  </Tabs>

- Initialize the class and call the method to create the index in your `Program.cs` file:

  ```csharp
  using query_quick_start;

  var indexService = new IndexService();
  indexService.CreateVectorIndex();
  ```

- Compile and run your project to create the index.

  ```bash
  dotnet run query-quick-start.csproj
  ```

  ```sh
  New search index named vector_index is building.
  Polling to check if the index is ready. This may take up to a minute.
  vector_index is ready for querying.

  ```

</Tab>

<Tab name="Go">

- Initialize your Go module:

  ```sh
  mkdir go-vector-quickstart && cd go-vector-quickstart
  go mod init go-vector-quickstart
  ```

- Add the Go Driver as a dependency in your project:

  ```sh
  go get go.mongodb.org/mongo-driver/v2/mongo
  ```

  For more detailed installation instructions, see the [MongoDB Go Driver documentation](https://www.mongodb.com/docs/drivers/go/current/quick-start/#std-label-golang-quickstart).

- Define the index.

  Create a file named `vector-index.go`. Copy and paste the following code into the file.

  ```go
  package main

  import (
  	"context"
  	"fmt"
  	"log"
  	"time"

  	"go.mongodb.org/mongo-driver/v2/bson"
  	"go.mongodb.org/mongo-driver/v2/mongo"
  	"go.mongodb.org/mongo-driver/v2/mongo/options"
  )

  func main() {
  	ctx := context.Background()

  	// Replace the placeholder with your Atlas connection string
  	const uri = "<connection-string>"

  	// Connect to your Atlas cluster
  	clientOptions := options.Client().ApplyURI(uri)
  	client, err := mongo.Connect(clientOptions)
  	if err != nil {
  		log.Fatalf("failed to connect to the server: %v", err)
  	}
  	defer func() { _ = client.Disconnect(ctx) }()

  	// Set the namespace
  	coll := client.Database("sample_mflix").Collection("embedded_movies")

  	// Define the index details
  	type vectorDefinitionField struct {
  		Type          string `bson:"type"`
  		Path          string `bson:"path"`
  		NumDimensions int    `bson:"numDimensions"`
  		Similarity    string `bson:"similarity"`
  		Quantization  string `bson:"quantization"`
  	}

  	type vectorDefinition struct {
  		Fields []vectorDefinitionField `bson:"fields"`
  	}

  	indexName := "vector_index"
  	opts := options.SearchIndexes().SetName(indexName).SetType("vectorSearch")

  	indexModel := mongo.SearchIndexModel{
  		Definition: vectorDefinition{
  			Fields: []vectorDefinitionField{{
  				Type:          "vector",
  				Path:          "plot_embedding",
  				NumDimensions: 1536,
  				Similarity:    "dotProduct",
  				Quantization:  "scalar"}},
  		},
  		Options: opts,
  	}

  	// Create the index
  	searchIndexName, err := coll.SearchIndexes().CreateOne(ctx, indexModel)
  	if err != nil {
  		log.Fatalf("failed to create the search index: %v", err)
  	}
  	log.Println("New search index named " + searchIndexName + " is building.")

  	// Await the creation of the index.
  	log.Println("Polling to check if the index is ready. This may take up to a minute.")
  	searchIndexes := coll.SearchIndexes()
  	var doc bson.Raw
  	for doc == nil {
  		cursor, err := searchIndexes.List(ctx, options.SearchIndexes().SetName(searchIndexName))
  		if err != nil {
  			fmt.Errorf("failed to list search indexes: %w", err)
  		}

  		if !cursor.Next(ctx) {
  			break
  		}

  		name := cursor.Current.Lookup("name").StringValue()
  		queryable := cursor.Current.Lookup("queryable").Boolean()
  		if name == searchIndexName && queryable {
  			doc = cursor.Current
  		} else {
  			time.Sleep(5 * time.Second)
  		}
  	}

  	log.Println(searchIndexName + " is ready for querying.")
  }

  ```

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

  This code also includes a polling mechanism to check if the index is ready to use.

- Specify the `<connection-string>`.

  <Tabs>

  <Tab name="Atlas Cluster">

  Your connection string should use the following format:

  ```
  mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
  ```

  Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

  </Tab>

  <Tab name="Local Deployment">

  Your connection string should use the following format:

  ```
  mongodb://localhost:<port-number>/?directConnection=true
  ```

  </Tab>

  </Tabs>

- Create the index.

  ```shell
  go run vector-index.go
  ```

  ```console
  2025/03/11 10:46:44 New search index named vector_index is building.
  2025/03/11 10:46:44 Polling to check if the index is ready. This may take up to a minute.
  2025/03/11 10:47:09 vector_index is ready for querying.
  ```

</Tab>

<Tab name="Java (Sync)">

- Add the Java driver version 5.2 or higher as a dependency in your project. Select one of the following tabs, depending on your package manager:

  <Tabs>

  <Tab name="Maven">

  If you are using Maven, add the following dependencies to the `dependencies` array in your project's `pom.xml` file:

  ```xml
  <dependencies>
     <!-- MongoDB Java Sync Driver v5.2.0 or later -->
     <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>mongodb-driver-sync</artifactId>
        <version>[5.2.0,)</version>
     </dependency>
  </dependencies>
  ```

  </Tab>

  </Tabs>

  <Tab name="Gradle">

  If you are using Gradle, add the following to the `dependencies` array in your project's `build.gradle` file:

  ```json
  dependencies {
     // MongoDB Java Sync Driver v5.2.0 or later
     implementation 'org.mongodb:mongodb-driver-sync:[5.2.0,)'
  }
  ```

  </Tab>

- Run your package manager to install the dependencies to your project.

  For more detailed installation instructions and version compatibility, see the [MongoDB Java Driver documentation](https://www.mongodb.com/docs/drivers/java/sync/current/quick-start/#std-label-add-mongodb-dependency).

- Create a file named `VectorIndex.java`. Copy and paste the following code into the file.

  ```java
  import com.mongodb.client.MongoClient;
  import com.mongodb.client.MongoClients;
  import com.mongodb.client.MongoCollection;
  import com.mongodb.client.MongoDatabase;
  import com.mongodb.client.model.SearchIndexModel;
  import com.mongodb.client.model.SearchIndexType;
  import org.bson.Document;
  import org.bson.conversions.Bson;

  import java.util.Collections;
  import java.util.List;

  public class VectorIndex {

      public static void main(String[] args) {

          // Replace the placeholder with your Atlas connection string
          String uri = "<connectionString>";

          // Connect to your Atlas cluster
          try (MongoClient mongoClient = MongoClients.create(uri)) {

              // Set the namespace
              MongoDatabase database = mongoClient.getDatabase("sample_mflix");
              MongoCollection<Document> collection = database.getCollection("embedded_movies");

              // Define the index details
              String indexName = "vector_index";
              Bson definition = new Document(
                  "fields",
                  Collections.singletonList(
                      new Document("type", "vector")
                          .append("path", "plot_embedding")
                          .append("numDimensions", 1536)
                          .append("similarity", "dotProduct")
                          .append("quantization", "scalar")));

              // Define the index model
              SearchIndexModel indexModel = new SearchIndexModel(
                  indexName,
                  definition,
                  SearchIndexType.vectorSearch());

              // Create the index using the defined model
              List<String> result = collection.createSearchIndexes(Collections.singletonList(indexModel));
              System.out.println("Successfully created vector index named: " + result.get(0));
              System.out.println("It may take up to a minute for the index to leave the BUILDING status and become queryable.");

              // Wait for Atlas to build the index
              System.out.println("Polling to confirm the index has left the BUILDING status.");
              // No special handling in case of a timeout. Custom handling can be implemented.
              waitForIndex(collection, indexName);
          }
      }

      /**
       * Polls the collection to check whether the specified index is ready to query.
       */
      public static <T> boolean waitForIndex(final MongoCollection<T> collection, final String indexName) {
          long startTime = System.nanoTime();
          long timeoutNanos = TimeUnit.SECONDS.toNanos(60);
          while (System.nanoTime() - startTime < timeoutNanos) {
              Document indexRecord = StreamSupport.stream(collection.listSearchIndexes().spliterator(), false)
                      .filter(index -> indexName.equals(index.getString("name")))
                      .findAny().orElse(null);
              if (indexRecord != null) {
                  if ("FAILED".equals(indexRecord.getString("status"))) {
                      throw new RuntimeException("Search index has FAILED status.");
                  }
                  if (indexRecord.getBoolean("queryable")) {
                      System.out.println(indexName + " index is ready to query");
                      return true;
                  }
              }
              try {
                  Thread.sleep(100); // busy-wait, avoid in production
              } catch (InterruptedException e) {
                  Thread.currentThread().interrupt();
                  throw new RuntimeException(e);
              }
          }
          return false;
      }
  }

  ```

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

  This code also includes a polling mechanism to check if the index is ready to use.

- Specify the `<connection-string>`.

  <Tabs>

  <Tab name="Atlas Cluster">

  Your connection string should use the following format:

  ```
  mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
  ```

  Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

  </Tab>

  <Tab name="Local Deployment">

  Your connection string should use the following format:

  ```
  mongodb://localhost:<port-number>/?directConnection=true
  ```

  </Tab>

  </Tabs>

- Run the file in your IDE, or execute a command from the command line to run the code.

  ```shell
  javac VectorIndex.java
  java VectorIndex
  ```

  ```console
  New search index named vector_index is building.
  Polling to check if the index is ready. This may take up to a minute.
  vector_index is ready for querying.

  ```

</Tab>

<Tab name="Kotlin (Coroutine)">

- Install the MongoDB Kotlin Coroutine Driver.

  For more detailed installation instructions and version compatibility, see the [MongoDB Kotlin Coroutine Driver documentation](https://www.mongodb.com/docs/drivers/kotlin/coroutine/current/quick-start/#std-label-kotlin-quickstart).

- Define the index.

  Create a file named `VectorIndex.kt`. Copy and paste the following code into the file.

  ```kotlin
  import com.mongodb.MongoException
  import com.mongodb.client.model.SearchIndexModel
  import com.mongodb.client.model.SearchIndexType
  import com.mongodb.kotlin.client.coroutine.MongoClient
  import kotlinx.coroutines.delay
  import kotlinx.coroutines.flow.toList
  import org.bson.Document
  import kotlinx.coroutines.runBlocking

  fun main() {
      // Replace the placeholder with your MongoDB deployment's connection string
      val uri = "<connection-string>"
      val mongoClient = MongoClient.create(uri)
      val database = mongoClient.getDatabase("sample_mflix")
      val collection = database.getCollection<Document>("embedded_movies")
      val indexName = "vector_index"
      val searchIndexModel = SearchIndexModel(
          indexName,
          Document(
              "fields",
              listOf(
                  Document("type", "vector")
                      .append("path", "plot_embedding")
                      .append("numDimensions", 1536)
                      .append("similarity", "dotProduct")
                      .append("quantization", "scalar")
              )
          ),
          SearchIndexType.vectorSearch()
      )

      runBlocking {
          try {
              collection.createSearchIndexes(listOf(searchIndexModel))
                  .collect { result ->
                      println("New search index named $result is building.")
                  }

              // Polling to check if the index is queryable
              println("Polling to check if the index is ready. This may take up to a minute.")
              var isQueryable = false
              while (!isQueryable) {
                  delay(5000L) // Poll every 5 seconds

                  val indexes = collection.listSearchIndexes().toList()
                  isQueryable = indexes.any { index ->
                      index.getString("name") == indexName && index.getBoolean("queryable")
                  }
                  if (isQueryable) {
                      println("$indexName is ready for querying.")
                  }
              }
          } catch (me: MongoException) {
              System.err.println("An error occurred: $me")
          }
      }
      mongoClient.close()
  }
  ```

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

  This code also includes a polling mechanism to check if the index is ready to use.

- Specify the `<connection-string>`.

  <Tabs>

  <Tab name="Atlas Cluster">

  Your connection string should use the following format:

  ```
  mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
  ```

  Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

  </Tab>

  <Tab name="Local Deployment">

  Your connection string should use the following format:

  ```
  mongodb://localhost:<port-number>/?directConnection=true
  ```

  </Tab>

  </Tabs>

- Run the `VectorIndex.kt` file in your IDE. The output should resemble the following:

  ```
  New search index named vector_index is building.
  Polling to check if the index is ready. This may take up to a minute.
  vector_index is ready for querying.

  ```

</Tab>

<Tab name="Kotlin (Sync)">

- Install the MongoDB Kotlin Sync Driver.

  For more detailed installation instructions and version compatibility, see the [MongoDB Kotlin Sync Driver documentation](https://www.mongodb.com/docs/languages/kotlin/kotlin-sync-driver/current/get-started/download-and-install/#std-label-kotlin-sync-download-install).

- Define the index.

  Create a file named `VectorIndex.kt`. Copy and paste the following code into the file.

  ```kotlin
  import com.mongodb.MongoException
  import com.mongodb.client.model.SearchIndexModel
  import com.mongodb.client.model.SearchIndexType
  import com.mongodb.kotlin.client.MongoClient
  import org.bson.Document

  fun main() {
      // Replace the placeholder with your MongoDB deployment's connection string
      val uri = "<connection-string>"
      val mongoClient = MongoClient.create(uri)
      val database = mongoClient.getDatabase("sample_mflix")
      val collection = database.getCollection<Document>("embedded_movies")
      val indexName = "vector_index"
      val searchIndexModel = SearchIndexModel(
          indexName,
          Document(
              "fields",
              listOf(
                  Document("type", "vector")
                      .append("path", "plot_embedding")
                      .append("numDimensions", 1536)
                      .append("similarity", "dotProduct")
              )
          ),
          SearchIndexType.vectorSearch()
      )

      try {
          val result = collection.createSearchIndexes(
              listOf(searchIndexModel)
          )
          println("New search index named ${result.get(0)} is building.")

          // Polling to check if the index is queryable
          println("Polling to check if the index is ready. This may take up to a minute.")
          var isQueryable = false
          while (!isQueryable) {
              val results = collection.listSearchIndexes()
              results.forEach { result ->
                  if (result.getString("name") == indexName && result.getBoolean("queryable")) {
                      println("$indexName is ready for querying.")
                      isQueryable = true
                  } else {
                      Thread.sleep(5000) // Poll every 5 seconds
                  }
              }
          }
      } catch (me: MongoException) {
          System.err.println("An error occurred: $me")
      }
      mongoClient.close()
  }

  ```

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

  This code also includes a polling mechanism to check if the index is ready to use.

- Specify the `<connection-string>`.

  <Tabs>

  <Tab name="Atlas Cluster">

  Your connection string should use the following format:

  ```
  mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
  ```

  Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

  </Tab>

  <Tab name="Local Deployment">

  Your connection string should use the following format:

  ```
  mongodb://localhost:<port-number>/?directConnection=true
  ```

  </Tab>

  </Tabs>

- Run the `VectorIndex.kt` file in your IDE. The output should resemble the following:

  ```
  New search index named vector_index is building.
  Polling to check if the index is ready. This may take up to a minute.
  vector_index is ready for querying.

  ```

</Tab>

<Tab name="Node.js">

- Add the MongoDB Node Driver as a dependency in your project:

  ```sh
  npm install mongodb
  ```

  The examples on this page assume your project manages modules as CommonJS modules. If you're using ES modules, instead, you must modify the import syntax.

- Define the index.

  Create a file named `vector-index.js`. Copy and paste the following code into the file.

  ```javascript
  const { MongoClient } = require("mongodb");

  // connect to your Atlas deployment
  const uri =  "<connectionString>";

  const client = new MongoClient(uri);

  async function run() {
     try {
       const database = client.db("sample_mflix");
       const collection = database.collection("embedded_movies");

       // define your Atlas Vector Search index
       const index = {
           name: "vector_index",
           type: "vectorSearch",
           definition: {
             "fields": [
               {
                 "type": "vector",
                 "numDimensions": 1536,
                 "path": "plot_embedding",
                 "similarity": "dotProduct",
                 "quantization": "scalar"
               }
             ]
           }
       }

       // run the helper method
       const result = await collection.createSearchIndex(index);
       console.log(`New search index named ${result} is building.`);

       // wait for the index to be ready to query
       console.log("Polling to check if the index is ready. This may take up to a minute.")
       let isQueryable = false;
       while (!isQueryable) {
         const cursor = collection.listSearchIndexes();
         for await (const index of cursor) {
           if (index.name === result) {
             if (index.queryable) {
               console.log(`${result} is ready for querying.`);
               isQueryable = true;
             } else {
               await new Promise(resolve => setTimeout(resolve, 5000));
             }
           }
         }
       }
     } finally {
       await client.close();
     }
  }
  run().catch(console.dir);

  ```

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

  This code also includes a polling mechanism to check if the index is ready to use.

- Specify the `<connection-string>`.

  <Tabs>

  <Tab name="Atlas Cluster">

  Your connection string should use the following format:

  ```
  mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
  ```

  Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

  </Tab>

  <Tab name="Local Deployment">

  Your connection string should use the following format:

  ```
  mongodb://localhost:<port-number>/?directConnection=true
  ```

  </Tab>

  </Tabs>

- Create the index.

  ```shell
  node vector-index.js
  ```

  ```console
  New search index named vector_index is building.
  Polling to check if the index is ready. This may take up to a minute.
  vector_index is ready for querying.

  ```

</Tab>

<Tab name="PHP">

- Install the MongoDB PHP Driver.

  For detailed installation instructions, see the [MongoDB PHP Library documentation](https://www.mongodb.com/docs/php-library/current/get-started/#std-label-php-download-and-install).

- Define the index.

  Create a file named `vector-index.php`. Copy and paste the following code into the file.

  ```php
  <?php

  require 'vendor/autoload.php';

  // Replace the placeholder with your Atlas connection string
  $uri = "<connection-string>";
  $client = new MongoDB\Client($uri);
  $collection = $client->sample_mflix->embedded_movies;
  $indexName = "vector_index";
  try {
      $result = $collection->createSearchIndexes(
          [[
              'name' => $indexName,
              'type' => 'vectorSearch',
              'definition' => [
                  'fields' => [[
                      'type' => 'vector',
                      'path' => 'plot_embedding',
                      'numDimensions' => 1536,
                      'similarity' => 'dotProduct',
                      'quantization' => 'scalar'
                  ]]
              ],
          ]]
      );
      echo "New search index named $result[0] is building." .PHP_EOL;

      // Polling for the index to become queryable
      echo "Polling to check if the index is ready. This may take up to a minute." .PHP_EOL;
      $isIndexQueryable = false;
      while (!$isIndexQueryable) {
          // List the search indexes
          $searchIndexes = $collection->listSearchIndexes();
          // Check if the index is present and queryable
          foreach ($searchIndexes as $index) {
              if ($index->name === $indexName) {
                  $isIndexQueryable = $index->queryable;
              }
          }
          if (!$isIndexQueryable) {
              sleep(5); // Wait for 5 seconds before polling again
          }
      }
      echo "$indexName is ready for querying." .PHP_EOL;
  }
  catch (MongoDB\Driver\Exception\Exception $e) {
      print 'Error creating the index: ' .$e->getMessage() .PHP_EOL;
  }

  ```

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

  This code also includes a polling mechanism to check if the index is ready to use.

- Specify the `<connection-string>`.

  <Tabs>

  <Tab name="Atlas Cluster">

  Your connection string should use the following format:

  ```
  mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
  ```

  Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

  </Tab>

  <Tab name="Local Deployment">

  Your connection string should use the following format:

  ```
  mongodb://localhost:<port-number>/?directConnection=true
  ```

  </Tab>

  </Tabs>

- Create the index.

  ```shell
  php vector-index.php
  ```

  ```console
  New search index named vector_index is building.
  Polling to check if the index is ready. This may take up to a minute.
  vector_index is ready for querying.

  ```

</Tab>

<Tab name="Python">

- Add the PyMongo Driver as a dependency in your project:

  ```sh
  pip install pymongo
  ```

  For more detailed installation instructions, see the [MongoDB Python Driver documentation](https://www.mongodb.com/docs/languages/python/pymongo-driver/get-started/download-and-install/#std-label-pymongo-get-started-download-and-install).

- Define the index.

  Create a file named `vector-index.py`. Copy and paste the following code into the file.

  ```python
  from pymongo.mongo_client import MongoClient
  from pymongo.operations import SearchIndexModel
  import time

  # Connect to your Atlas deployment
  uri = "<connectionString>"
  client = MongoClient(uri)

  # Access your database and collection
  database = client["sample_mflix"]
  collection = database["embedded_movies"]

  # Create your index model, then create the search index
  search_index_model = SearchIndexModel(
    definition={
      "fields": [
        {
          "type": "vector",
          "path": "plot_embedding",
          "numDimensions": 1536,
          "similarity": "dotProduct",
          "quantization": "scalar"
        }
      ]
    },
    name="vector_index",
    type="vectorSearch"
  )

  result = collection.create_search_index(model=search_index_model)
  print("New search index named " + result + " is building.")

  # Wait for initial sync to complete
  print("Polling to check if the index is ready. This may take up to a minute.")
  predicate=None
  if predicate is None:
    predicate = lambda index: index.get("queryable") is True

  while True:
    indices = list(collection.list_search_indexes(result))
    if len(indices) and predicate(indices[0]):
      break
    time.sleep(5)
  print(result + " is ready for querying.")

  client.close()

  ```

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

  This code also includes a polling mechanism to check if the index is ready to use.

- Specify the `<connection-string>`.

  <Tabs>

  <Tab name="Atlas Cluster">

  Your connection string should use the following format:

  ```
  mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
  ```

  Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

  </Tab>

  <Tab name="Local Deployment">

  Your connection string should use the following format:

  ```
  mongodb://localhost:<port-number>/?directConnection=true
  ```

  </Tab>

  </Tabs>

- Create the index.

  ```shell
  python vector-index.py
  ```

  ```console
  New search index named vector_index is building.
  Polling to check if the index is ready. This may take up to a minute.
  vector_index is ready for querying.

  ```

</Tab>

<Tab name="Ruby">

- When the sample data finishes loading, click Create Search Index.

- Make the following selections on the page and then click Next.

  `sample_mflix``embedded_movies`Atlas Vector Search<table>

  <tr>
  <td>
  Search Type

  </td>
  <td>
  Select the Atlas Vector Search index type.

  </td>
  </tr>
  <tr>
  <td>
  Index Name and Data Source

  </td>
  <td>
  Specify the following information:

  - Index Name: `vector_index`

  - Database and Collection:

    - `sample_mflix`

    - `embedded_movies`

  </td>
  </tr>
  <tr>
  <td>
  Configuration Method

  </td>
  <td>
  For a guided experience, select Visual Editor.To edit the raw index definition, select JSON Editor.

  </td>
  </tr>
  </table>

- Define the index.

  <Tabs>

  <Tab name="Visual Editor">

  Atlas automatically detects fields that contain vector embeddings, as well as their corresponding dimensions. For the `sample_mflix.embedded_movies` collection, the `plot_embedding` field displays.

  To configure the index, do the following:

  - Select Dot Product from the Similarity Method dropdown.

  - Click the Advanced dropdown menu and select Scalar to [automatically quantize](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization) the embeddings in the field.

  </Tab>

  <Tab name="JSON Editor">

  Copy and paste the following [vector search index definition](https://mongodb.com/docs/atlas/atlas-vector-search/tutorials/vector-search-quick-start/#std-label-vector-search-quickstart-vector-index-definition) into the JSON Editor.

  ```
  {
      "fields": [{
      "type": "vector",
      "path": "plot_embedding",
      "numDimensions": 1536,
      "similarity": "dotProduct"
      "quantization": "scalar"
      }]
  }
  ```

  </Tab>

  </Tabs>

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

- Click Next.

- Click Create Vector Search Index.

  The index should take about one minute to build. When your vector index is finished building, the Status column reads Active.

</Tab>

<Tab name="Rust">

- Install the Rust driver for MongoDB.

  For more detailed installation instructions, see the [MongoDB Rust Driver documentation](https://www.mongodb.com/docs/drivers/rust/current/quick-start/download-and-install/#std-label-rust-quick-start-download-and-install).

- Define the index.

  In the `/src` directory of your project, create a file named `vector_index.rs`. Copy and paste the following code into the file.

  <Tabs>

  <Tab name="">

  ```rust
  use std::ops::Index;
  use std::time::Duration;
  use futures::{TryStreamExt};
  use mongodb::{bson::{Document, doc}, Client, Collection, SearchIndexModel};
  use mongodb::SearchIndexType::VectorSearch;
  use tokio::time::sleep;

  #[tokio::main]
  pub(crate) async fn vector_index() {
      // Replace the placeholder with your Atlas connection string
      let uri = "<connection_string>";

      // Create a new client and connect to the server
      let client = Client::with_uri_str(uri).await.unwrap();

      // Get a handle on the movies collection
      let database = client.database("sample_mflix");
      let my_coll: Collection<Document> = database.collection("embedded_movies");

      let index_name = "vector_index";
      let search_index_def = SearchIndexModel::builder()
          .definition(doc! {
              "fields": vec! {doc! {
                  "type": "vector",
                  "path": "plot_embedding",
                  "numDimensions": 1536,
                  "similarity": "dotProduct",
                  "quantization": "scalar"
              }}
          })
          .name(index_name.to_string())
          .index_type(VectorSearch)
          .build();

      let models = vec![search_index_def];
      let result = my_coll.create_search_indexes(models).await;
      if let Err(e) = result {
          eprintln!("There was an error creating the search index: {}", e);
          std::process::exit(1)
      } else {
          println!("New search index named {} is building.", result.unwrap().index(0));
      }

      // Polling for the index to become queryable
      println!("Polling to check if the index is ready. This may take up to a minute.");
      let mut is_index_queryable = false;
      while !is_index_queryable {
          // List the search indexes
          let mut search_indexes = my_coll.list_search_indexes().await.unwrap();
          // Check if the index is present and queryable
          while let Some(index) = search_indexes.try_next().await.unwrap() {
              let retrieved_name = index.get_str("name");
              if retrieved_name.unwrap().to_string() == index_name {
                  is_index_queryable = index.get_bool("queryable").unwrap();
              }
          }
          if !is_index_queryable {
              sleep(Duration::from_secs(5)).await; // Wait for 5 seconds before polling again
          }
      }
      println!("{} is ready for querying.", index_name);
  }

  ```

  </Tab>

  <Tab name="">

  ```rust
  use std::ops::Index;
  use std::time::Duration;
  use std::thread::sleep;
  use mongodb::{
      bson::{doc, Document},
      Client, Collection, SearchIndexModel,
  };
  use mongodb::options::ClientOptions;
  use mongodb::SearchIndexType::VectorSearch;

  pub(crate) fn vector_index() {
      // Replace the placeholder with your Atlas connection string
      let uri = "<connection_string>";

      // Create a new client and connect to the server
      let options = ClientOptions::parse(uri).run().unwrap();
      let client = Client::with_options(options).unwrap();

      // Get a handle on the movies collection
      let database = client.database("sample_mflix");
      let my_coll: Collection<Document> = database.collection("embedded_movies");

      let index_name = "vector_index";
      let search_index_def = SearchIndexModel::builder()
          .definition(doc! {
              "fields": vec! {doc! {
                  "type": "vector",
                  "path": "plot_embedding",
                  "numDimensions": 1536,
                  "similarity": "dotProduct"
              }}
          })
          .name(index_name.to_string())
          .index_type(VectorSearch)
          .build();

      let models = vec![search_index_def];
      let result = my_coll.create_search_indexes(models).run();
      if let Err(e) = result {
          eprintln!("There was an error creating the search index: {}", e);
          std::process::exit(1)
      } else {
          println!("New search index named {} is building.", result.unwrap().index(0));
      }

      // Polling for the index to become queryable
      println!("Polling to check if the index is ready. This may take up to a minute.");
      let mut is_index_queryable = false;
      while !is_index_queryable {
          // List the search indexes
          let search_indexes = my_coll.list_search_indexes().run().unwrap();

          // Check if the index is present and queryable
          for index in search_indexes {
              let unwrapped_index = index.unwrap();
              let retrieved_name = unwrapped_index.get_str("name").unwrap();
              if retrieved_name == index_name {
                  is_index_queryable = unwrapped_index.get_bool("queryable").unwrap_or(false);
              }
          }

          if !is_index_queryable {
              sleep(Duration::from_secs(5)); // Wait for 5 seconds before polling again
          }
      }
      println!("{} is ready for querying.", index_name);
  }

  ```

  </Tab>

  </Tabs>

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

  This code also includes a polling mechanism to check if the index is ready to use.

- Specify the `<connection-string>`.

  <Tabs>

  <Tab name="Atlas Cluster">

  Your connection string should use the following format:

  ```
  mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
  ```

  Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

  </Tab>

  <Tab name="Local Deployment">

  Your connection string should use the following format:

  ```
  mongodb://localhost:<port-number>/?directConnection=true
  ```

  </Tab>

  </Tabs>

- Call the function from your `main.rs`.

  ```rust
  mod vector_index;

  fn main() {
     vector_index::vector_index();
  }
  ```

- Run the file in your IDE, or execute a command from the command line to run the code.

  ```shell
  cargo run
  ```

  ```console
  New search index named vector_index is building.
  Polling to check if the index is ready. This may take up to a minute.
  vector_index is ready for querying.

  ```

</Tab>

<Tab name="Scala">

- Install the MongoDB Scala Driver.

  For installation instructions based on your environment and the version of Scala you are using, refer to the

  [MongoDB Scala Driver documentation](https://www.mongodb.com/docs/languages/scala/scala-driver/current/get-started/#std-label-scala-quick-start-download-and-install).

- Create a new Scala project with the tools you normally use. For this quick start, we create a project named `quick-start`, by using [sbt](https://www.scala-sbt.org).

  ```sh
  sbt new scala/hello-world.g8
  ```

  When prompted, name the application `quick-start`.

- Navigate to your `quick-start` project and create a file named `VectorIndex.scala`. Copy and paste the following code into the file.

  ```scala
  import org.mongodb.scala._
  import org.mongodb.scala.model._
  import com.mongodb.client.model.SearchIndexType

  class VectorIndex {
    def createIndex(): Unit = {
      val collection =
        MongoClient("<connection-string>")
          .getDatabase("sample_mflix")
          .getCollection("embedded_movies")
      val indexName = "vector_index"
      val indexNameAsOption = Option(indexName)
      val indexDefinition = Document(
        "fields" -> List(
          Document(
            "type" -> "vector",
            "path" -> "plot_embedding",
            "numDimensions" -> 1536,
            "similarity" -> "dotProduct",
            "quantization" -> "scalar"
          )
        )
      )
      val indexType = Option(SearchIndexType.vectorSearch())
      val indexModel: SearchIndexModel = SearchIndexModel(
        indexNameAsOption, indexDefinition, indexType
      )
      collection.createSearchIndexes(List(indexModel)).foreach { doc => println(s"New search index named $doc is building.") }
      Thread.sleep(2000)
      println("Polling to check if the index is ready. This may take up to a minute.")
      var indexReady = false
      while (!indexReady) {
        val searchIndexes = collection.listSearchIndexes()
        searchIndexes.foreach { current =>
          val document = current.toBsonDocument()
          val name = document.get("name").asString().getValue
          val queryable = document.get("queryable").asBoolean().getValue
          if (name == indexName && queryable) {
            indexReady = true
          }
        }
        if (!indexReady) {
          Thread.sleep(5000)
        }
      }
      println(s"$indexName is ready for querying.")
    }
  }
  ```

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

  This code also includes a polling mechanism to check if the index is ready to use.

- Specify the `<connection-string>`.

  <Tabs>

  <Tab name="Atlas Cluster">

  Your connection string should use the following format:

  ```
  mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
  ```

  Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

  </Tab>

  <Tab name="Local Deployment">

  Your connection string should use the following format:

  ```
  mongodb://localhost:<port-number>/?directConnection=true
  ```

  </Tab>

  </Tabs>

- Create a class instance and call the function in your project's `Main.scala` file.

  ```scala
  object Main extends App {
     private val indexInstance = new VectorIndex
     indexInstance.createIndex()
  }
  ```

- Run the file in your IDE, or execute a command from the command line to run the code.

  There are many tools and environments in which Scala runs. In this example, we run the new Scala project by starting the sbt server with the `sbt` command, then typing `~run`.

  ```shell
  sbt:quick-start> ~run
  ```

  ```console
  New search index named vector_index is building.
  Polling to check if the index is ready. This may take up to a minute.
  vector_index is ready for querying.

  ```

</Tab>

</Tabs>

</Tab>

<Tab name="Local Deployment">

### Install the dependencies.

- Install the [Atlas CLI](https://www.mongodb.com/docs/atlas/cli/current/install-atlas-cli/).

  If you use [Homebrew](https://brew.sh/#install), you can run the following command in your terminal:

  ```
  brew install mongodb-atlas-cli
  ```

  For installation instructions on other operating systems, see [Install the Atlas CLI](https://www.mongodb.com/docs/atlas/cli/current/install-atlas-cli/)

- Install [Docker](https://www.docker.com/).

  Docker requires a network connection for pulling and caching MongoDB images.

  - For MacOS or Windows, install [Docker Desktop v4.31+](https://docs.docker.com/desktop/release-notes/#4310).

  - For Linux, install [Docker Engine v27.0+](https://docs.docker.com/engine/release-notes/27.0/).

  - For RHEL, you can also use [Podman v5.0+](https://podman.io).

- Install the MongoDB Database Tools.

  You must install the [MongoDB Command Line Database Tools](https://fastdl.mongodb.org/tools/db/mongodb-database-tools-macos-arm64-100.10.0.zip) to access the `mongorestore` command, which you'll use to load the sample data.

### Set up your local Atlas deployment.

- If you don't have an existing Atlas account, run `atlas setup` in your terminal or [create a new account](https://account.mongodb.com/account/register?tck=docs_atlas).

- Run `atlas deployments setup` and follow the prompts to create a local deployment. When prompted to connect to the deployment, select `skip`.

  For detailed instructions, see [Create a Local Atlas Deployment](https://www.mongodb.com/docs/atlas/cli/current/atlas-cli-deploy-local/#create-a-local-atlas-deployment-1).

### Load the sample data.

- Run the following command in your terminal to download the sample data:

  ```
  curl  https://atlas-education.s3.amazonaws.com/sampledata.archive -o sampledata.archive
  ```

- Run the following command to load the data into your deployment, replacing `<port-number>` with the port where you're hosting the deployment:

  ```
  mongorestore --archive=sampledata.archive --port=<port-number>
  ```

### Create a Vector Search index.

<Tabs>

<Tab name="Atlas UI">

- Create a file named `vector-index.json`

- Copy and paste the following index definition into the JSON (Javascript Object Notation) file.

  ```
  {
      "database": "sample_mflix",
      "collectionName": "embedded_movies",
      "type": "vectorSearch",
      "name": "vector_index",
      "fields": [
          {
          "type": "vector",
          "path": "plot_embedding",
          "numDimensions": 1536,
          "similarity": "dotProduct"
          }
      ]
  }
  ```

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

- Save the file, and then run the following command in your terminal, replacing `<path-to-file>` with the path to the `vector-index.json` file that you created.

  ```
  atlas deployments search indexes create --file <path-to-file>
  ```

</Tab>

<Tab name="MongoDB Shell">

- Connect to the Atlas cluster using [`mongosh`](https://www.mongodb.com/docs/mongodb-shell/#mongodb-binary-bin.mongosh).

  In a terminal window, run `atlas deployments connect` and follow the prompts to connect to your local Atlas deployment via [`mongosh`](https://www.mongodb.com/docs/mongodb-shell/#mongodb-binary-bin.mongosh). For detailed instructions on connecting, see [Manage a Local Atlas Deployment](https://www.mongodb.com/docs/atlas/cli/current/atlas-cli-deploy-local/#manage-a-local-atlas-deployment).

- Switch to the database that contains the collection for which you want to create the index.

  ```shell
  use sample_mflix
  ```

  ```shell
  switched to db sample_mflix
  ```

- Run the [`db.collection.createSearchIndex()`](https://www.mongodb.com/docs/manual/reference/method/db.collection.createSearchIndex/#mongodb-method-db.collection.createSearchIndex) method.

  ```shell
  db.embedded_movies.createSearchIndex(
    "vector_index",
    "vectorSearch",
    {
      "fields": [
        {
          "type": "vector",
          "path": "plot_embedding",
          "numDimensions": 1536,
          "similarity": "dotProduct",
          "quantization": "scalar"
        }
      ]
    }
  );
  ```

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

</Tab>

<Tab name="C">

- Install the MongoDB C Driver.

  For detailed installation instructions, refer to the [MongoDB C Driver documentation](https://www.mongodb.com/docs/languages/c/c-driver/current/libmongoc/tutorials/obtaining-libraries/).

- Create a new directory called `query-quick-start`.

  ```bash
  mkdir query-quick-start
  ```

- Enter the directory, and create a `CMakeLists.txt` file.

  ```bash
  cd query-quick-start
  touch CMakeLists.txt
  ```

  Copy and paste the following lines into the `CMakeLists.txt` file:

  ```console
  cmake_minimum_required(VERSION 3.30)

  project(atlas-vector-search-quick-start)

  # Specify the minimum version for creating a vector index.
  find_package (mongoc-1.0 1.28.0 REQUIRED)

  add_executable(atlas-vector-search-quick-start
    vector_index.c
  )

  target_link_libraries (atlas-vector-search-quick-start PRIVATE mongo::mongoc_shared)
  ```

- Define the index.

  Create a file named `vector_index.c`. Copy and paste the following code into the file.

  ```c
  #include <bson/bson.h>
  #include <mongoc/mongoc.h>
  #include <stdio.h>

  int main(void) {
    mongoc_client_t *client;
    mongoc_collection_t *collection;
    bson_error_t error;
    char database_name[] = "sample_mflix";
    char collection_name[] = "embedded_movies";
    char index_name[] = "vector_index";

    mongoc_init();

    // Replace the placeholder with your Atlas connection string
    client =  mongoc_client_new("<connection-string>");

    // Connect to your Atlas cluster
    collection = mongoc_client_get_collection (client, database_name, collection_name);

    bson_t cmd;
    // Create search index command.
    {
      char *cmd_str = bson_strdup_printf (
          BSON_STR ({
                      "createSearchIndexes" : "%s",
                      "indexes" : [{
                        "definition": {
                          "fields": [{
                            "type": "vector",
                            "path": "plot_embedding",
                            "numDimensions": 1536,
                            "similarity": "dotProduct",
                            "quantization": "scalar"
                          }]
                        },
                        "name": "%s",
                        "type": "vectorSearch"
                      }]
                    }),
          collection_name, index_name);
      if (!bson_init_from_json(&cmd, cmd_str, -1, &error)) {
        printf("Failed to initialize BSON: %s\n", error.message);
        bson_free(cmd_str);
        return 1;
      }
      bson_free (cmd_str);
    }
    if (!mongoc_collection_command_simple (collection, &cmd, NULL /* read_prefs */, NULL /* reply */, &error)) {
      bson_destroy (&cmd);
      printf ("Failed to run createSearchIndexes: %s", error.message);
      return 1;
    } else {
      printf ("New search index named %s is building.\n", index_name);
      bson_destroy (&cmd);
    }

    // Polling for index status
    printf("Polling to check if the index is ready. This may take up to a minute.\n");
    int queryable = 0;
    while (!queryable) {
      const char *pipeline_str = "{\"pipeline\": [{\"$listSearchIndexes\": {}}]}";
      bson_t pipeline;
      if (!bson_init_from_json(&pipeline, pipeline_str, -1, &error)) {
        printf("Failed to initialize pipeline BSON: %s\n", error.message);
        break; // Exit the loop on error
      }
      mongoc_cursor_t *cursor = mongoc_collection_aggregate(collection, MONGOC_QUERY_NONE, &pipeline, NULL, NULL);
      const bson_t *got;
      // Check if the cursor returns any documents
      int found_index = 0;
      while (mongoc_cursor_next(cursor, &got)) {
        bson_iter_t iter;
        if (bson_iter_init(&iter, got) && bson_iter_find(&iter, "name")) {
          const char *name = bson_iter_utf8(&iter, NULL);
          if (strcmp(name, index_name) == 0) {
            found_index = 1; // Index found
            bson_iter_find(&iter, "queryable");
            queryable = bson_iter_bool(&iter);
            break; // Exit the loop since we found the index
          }
        }
      }
      if (mongoc_cursor_error(cursor, &error)) {
        printf("Failed to run $listSearchIndexes: %s\n", error.message);
        break; // Exit the loop on error
      }
      if (!found_index) {
        printf("Index %s not found yet. Retrying...\n", index_name);
      }
      bson_destroy(&pipeline);
      mongoc_cursor_destroy(cursor);
      sleep(5); // Sleep for 5 seconds before checking again
    }
    if (queryable) {
      printf("%s is ready for querying.\n", index_name);
    } else {
      printf("Error occurred or index not found.\n");
    }

    // Cleanup
    mongoc_collection_destroy(collection);
    mongoc_client_destroy(client);
    mongoc_cleanup();

    return 0;
  }
  ```

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

  This code also includes a polling mechanism to check if the index is ready to use.

- Specify the `<connection-string>`.

  <Tabs>

  <Tab name="Atlas Cluster">

  Your connection string should use the following format:

  ```
  mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
  ```

  Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

  </Tab>

  <Tab name="Local Deployment">

  Your connection string should use the following format:

  ```
  mongodb://localhost:<port-number>/?directConnection=true
  ```

  </Tab>

  </Tabs>

- Create and enter the `/build` directory:

  ```bash
  mkdir build && cd build
  ```

- Prepare the project.

  ```bash
  cmake ../
  ```

- Build the app.

  ```bash
  cmake --build .
  ```

- Execute the app to create the index.

  ```bash
  ./atlas-vector-search-quick-start
  ```

  ```sh
  New search index named vector_index is building.
  Polling to check if the index is ready. This may take up to a minute.
  vector_index is ready for querying.

  ```

</Tab>

<Tab name="C++11">

- Install the MongoDB C++ Driver.

  For detailed installation instructions, refer to the [MongoDB C++ Driver documentation](https://www.mongodb.com/docs/languages/cpp/cpp-driver/current/installation/).

- Create a new directory called `query-quick-start`.

  ```bash
  mkdir query-quick-start
  ```

- Enter the directory, and create a `CMakeLists.txt` file.

  ```bash
  cd query-quick-start
  touch CMakeLists.txt
  ```

  Copy and paste the following lines into the `CMakeLists.txt` file:

  ```console
  cmake_minimum_required(VERSION 3.30)

  project(query_quick_start)

  set(CMAKE_CXX_STANDARD 17)

  # Specify the minimum version for creating a vector index.
  find_package(mongocxx 3.11.0 REQUIRED)
  find_package(bsoncxx REQUIRED)

  add_executable(query_quick_start
    vector_index.cpp
  )

  target_link_libraries(query_quick_start PRIVATE mongo::mongocxx_shared)
  target_link_libraries(query_quick_start PRIVATE mongo::bsoncxx_shared)
  ```

- Define the index.

  Create a file named `vector_index.cpp`. Copy and paste the following code into the file.

  ```cpp
  #include <bsoncxx/builder/basic/document.hpp>
  #include <iostream>
  #include <mongocxx/client.hpp>
  #include <mongocxx/instance.hpp>
  #include <mongocxx/search_index_view.hpp>
  #include <mongocxx/uri.hpp>
  #include <thread>

  using bsoncxx::builder::basic::kvp;
  using bsoncxx::builder::basic::make_array;
  using bsoncxx::builder::basic::make_document;

  int main() {
    try {
      mongocxx::instance inst{};

      // Replace the placeholder with your Atlas connection string
      const auto uri = mongocxx::uri{"<connection-string>"};

      // Connect to your Atlas cluster
      mongocxx::client conn{uri};
      auto db = conn["sample_mflix"];
      auto collection = db["embedded_movies"];

      auto siv = collection.search_indexes();
      std::string name = "vector_index";
      auto type = "vectorSearch";
      auto definition = make_document(
          kvp("fields",
              make_array(make_document(
                  kvp("type", "vector"), kvp("path", "plot_embedding"),
                  kvp("numDimensions", 1536), kvp("similarity", "dotProduct"),
                  kvp("quantization", "scalar")))));
      auto model =
          mongocxx::search_index_model(name, definition.view()).type(type);
      siv.create_one(model);
      std::cout << "New search index named " << name << " is building."
                << std::endl;

      // Polling for index status
      std::cout << "Polling to check if the index is ready. This may take up to "
                   "a minute."
                << std::endl;
      bool queryable = false;
      while (!queryable) {
        auto indexes = siv.list();
        for (const auto& index : indexes) {
          if (index["name"].get_value() == name) {
            queryable = index["queryable"].get_bool();
          }
        }
        if (!queryable) {
          std::this_thread::sleep_for(std::chrono::seconds(5));
        }
      }
      std::cout << name << " is ready for querying." << std::endl;
    } catch (const std::exception& e) {
      std::cout << "Exception: " << e.what() << std::endl;
    }
    return 0;
  }

  ```

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

  This code also includes a polling mechanism to check if the index is ready to use.

- Specify the `<connection-string>`.

  <Tabs>

  <Tab name="Atlas Cluster">

  Your connection string should use the following format:

  ```
  mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
  ```

  Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

  </Tab>

  <Tab name="Local Deployment">

  Your connection string should use the following format:

  ```
  mongodb://localhost:<port-number>/?directConnection=true
  ```

  </Tab>

  </Tabs>

- Create and enter the `/build` directory:

  ```bash
  mkdir build && cd build
  ```

- Prepare the project.

  ```bash
  cmake ../
  ```

- Build the app.

  ```bash
  cmake --build .
  ```

- Execute the app to create the index.

  ```bash
  ./query_quick_start
  ```

  ```sh
  New search index named vector_index is building.
  Polling to check if the index is ready. This may take up to a minute.
  vector_index is ready for querying.

  ```

</Tab>

<Tab name="C#">

- called `query-quick-start` and initialize your .NET/C# project.

  ```bash
  mkdir query-quick-start
  cd query-quick-start
  dotnet new console
  ```

- Run the following command to add the .NET/C# Driver to your project as a dependency.

  ```bash
  dotnet add package MongoDB.Driver
  ```

  For more detailed installation instructions, see the [MongoDB C# Driver documentation](https://www.mongodb.com/docs/drivers/csharp/current/quick-start/#std-label-csharp-quickstart).

- Define the index.

  Create a file named `IndexService.cs`. Copy and paste the following code into the file.

  ```csharp
  namespace query_quick_start;

  using MongoDB.Bson;
  using MongoDB.Driver;
  using System;
  using System.Threading;

  public class IndexService
  {
      // Replace the placeholder with your Atlas connection string
      private const string MongoConnectionString = "<connection-string>";
      public void CreateVectorIndex()
      {
          try
          {
              // Connect to your Atlas cluster
              var client = new MongoClient(MongoConnectionString);
              var database = client.GetDatabase("sample_mflix");
              var collection = database.GetCollection<BsonDocument>("embedded_movies");

              var searchIndexView = collection.SearchIndexes;
              var name = "vector_index";
              var type = SearchIndexType.VectorSearch;

              var definition = new BsonDocument
              {
                  { "fields", new BsonArray
                      {
                          new BsonDocument
                          {
                              { "type", "vector" },
                              { "path", "plot_embedding" },
                              { "numDimensions", 1536 },
                              { "similarity", "dotProduct" },
                              { "quantization", "scalar" }
                          }
                      }
                  }
              };

              var model = new CreateSearchIndexModel(name, type, definition);

              searchIndexView.CreateOne(model);
              Console.WriteLine($"New search index named {name} is building.");

              // Polling for index status
              Console.WriteLine("Polling to check if the index is ready. This may take up to a minute.");
              bool queryable = false;
              while (!queryable)
              {
                  var indexes = searchIndexView.List();
                  foreach (var index in indexes.ToEnumerable())
                  {
                      if (index["name"] == name)
                      {
                          queryable = index["queryable"].AsBoolean;
                      }
                  }
                  if (!queryable)
                  {
                      Thread.Sleep(5000);
                  }
              }
              Console.WriteLine($"{name} is ready for querying.");
          }
          catch (Exception e)
          {
              Console.WriteLine($"Exception: {e.Message}");
          }
      }
  }

  ```

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

  This code also includes a polling mechanism to check if the index is ready to use.

- Specify the `<connection-string>`.

  <Tabs>

  <Tab name="Atlas Cluster">

  Your connection string should use the following format:

  ```
  mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
  ```

  Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

  </Tab>

  <Tab name="Local Deployment">

  Your connection string should use the following format:

  ```
  mongodb://localhost:<port-number>/?directConnection=true
  ```

  </Tab>

  </Tabs>

- Initialize the class and call the method to create the index in your `Program.cs` file:

  ```csharp
  using query_quick_start;

  var indexService = new IndexService();
  indexService.CreateVectorIndex();
  ```

- Compile and run your project to create the index.

  ```bash
  dotnet run query-quick-start.csproj
  ```

  ```sh
  New search index named vector_index is building.
  Polling to check if the index is ready. This may take up to a minute.
  vector_index is ready for querying.

  ```

</Tab>

<Tab name="Go">

- Initialize your Go module:

  ```sh
  mkdir go-vector-quickstart && cd go-vector-quickstart
  go mod init go-vector-quickstart
  ```

- Add the Go Driver as a dependency in your project:

  ```sh
  go get go.mongodb.org/mongo-driver/v2/mongo
  ```

  For more detailed installation instructions, see the [MongoDB Go Driver documentation](https://www.mongodb.com/docs/drivers/go/current/quick-start/#std-label-golang-quickstart).

- Define the index.

  Create a file named `vector-index.go`. Copy and paste the following code into the file.

  ```go
  package main

  import (
  	"context"
  	"fmt"
  	"log"
  	"time"

  	"go.mongodb.org/mongo-driver/v2/bson"
  	"go.mongodb.org/mongo-driver/v2/mongo"
  	"go.mongodb.org/mongo-driver/v2/mongo/options"
  )

  func main() {
  	ctx := context.Background()

  	// Replace the placeholder with your Atlas connection string
  	const uri = "<connection-string>"

  	// Connect to your Atlas cluster
  	clientOptions := options.Client().ApplyURI(uri)
  	client, err := mongo.Connect(clientOptions)
  	if err != nil {
  		log.Fatalf("failed to connect to the server: %v", err)
  	}
  	defer func() { _ = client.Disconnect(ctx) }()

  	// Set the namespace
  	coll := client.Database("sample_mflix").Collection("embedded_movies")

  	// Define the index details
  	type vectorDefinitionField struct {
  		Type          string `bson:"type"`
  		Path          string `bson:"path"`
  		NumDimensions int    `bson:"numDimensions"`
  		Similarity    string `bson:"similarity"`
  		Quantization  string `bson:"quantization"`
  	}

  	type vectorDefinition struct {
  		Fields []vectorDefinitionField `bson:"fields"`
  	}

  	indexName := "vector_index"
  	opts := options.SearchIndexes().SetName(indexName).SetType("vectorSearch")

  	indexModel := mongo.SearchIndexModel{
  		Definition: vectorDefinition{
  			Fields: []vectorDefinitionField{{
  				Type:          "vector",
  				Path:          "plot_embedding",
  				NumDimensions: 1536,
  				Similarity:    "dotProduct",
  				Quantization:  "scalar"}},
  		},
  		Options: opts,
  	}

  	// Create the index
  	searchIndexName, err := coll.SearchIndexes().CreateOne(ctx, indexModel)
  	if err != nil {
  		log.Fatalf("failed to create the search index: %v", err)
  	}
  	log.Println("New search index named " + searchIndexName + " is building.")

  	// Await the creation of the index.
  	log.Println("Polling to check if the index is ready. This may take up to a minute.")
  	searchIndexes := coll.SearchIndexes()
  	var doc bson.Raw
  	for doc == nil {
  		cursor, err := searchIndexes.List(ctx, options.SearchIndexes().SetName(searchIndexName))
  		if err != nil {
  			fmt.Errorf("failed to list search indexes: %w", err)
  		}

  		if !cursor.Next(ctx) {
  			break
  		}

  		name := cursor.Current.Lookup("name").StringValue()
  		queryable := cursor.Current.Lookup("queryable").Boolean()
  		if name == searchIndexName && queryable {
  			doc = cursor.Current
  		} else {
  			time.Sleep(5 * time.Second)
  		}
  	}

  	log.Println(searchIndexName + " is ready for querying.")
  }

  ```

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

  This code also includes a polling mechanism to check if the index is ready to use.

- Specify the `<connection-string>`.

  <Tabs>

  <Tab name="Atlas Cluster">

  Your connection string should use the following format:

  ```
  mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
  ```

  Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

  </Tab>

  <Tab name="Local Deployment">

  Your connection string should use the following format:

  ```
  mongodb://localhost:<port-number>/?directConnection=true
  ```

  </Tab>

  </Tabs>

- Create the index.

  ```shell
  go run vector-index.go
  ```

  ```console
  2025/03/11 10:46:44 New search index named vector_index is building.
  2025/03/11 10:46:44 Polling to check if the index is ready. This may take up to a minute.
  2025/03/11 10:47:09 vector_index is ready for querying.
  ```

</Tab>

<Tab name="Java (Sync)">

- Add the Java driver version 5.2 or higher as a dependency in your project. Select one of the following tabs, depending on your package manager:

  <Tabs>

  <Tab name="Maven">

  If you are using Maven, add the following dependencies to the `dependencies` array in your project's `pom.xml` file:

  ```xml
  <dependencies>
     <!-- MongoDB Java Sync Driver v5.2.0 or later -->
     <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>mongodb-driver-sync</artifactId>
        <version>[5.2.0,)</version>
     </dependency>
  </dependencies>
  ```

  </Tab>

  </Tabs>

  <Tab name="Gradle">

  If you are using Gradle, add the following to the `dependencies` array in your project's `build.gradle` file:

  ```json
  dependencies {
     // MongoDB Java Sync Driver v5.2.0 or later
     implementation 'org.mongodb:mongodb-driver-sync:[5.2.0,)'
  }
  ```

  </Tab>

- Run your package manager to install the dependencies to your project.

  For more detailed installation instructions and version compatibility, see the [MongoDB Java Driver documentation](https://www.mongodb.com/docs/drivers/java/sync/current/quick-start/#std-label-add-mongodb-dependency).

- Create a file named `VectorIndex.java`. Copy and paste the following code into the file.

  ```java
  import com.mongodb.client.MongoClient;
  import com.mongodb.client.MongoClients;
  import com.mongodb.client.MongoCollection;
  import com.mongodb.client.MongoDatabase;
  import com.mongodb.client.model.SearchIndexModel;
  import com.mongodb.client.model.SearchIndexType;
  import org.bson.Document;
  import org.bson.conversions.Bson;

  import java.util.Collections;
  import java.util.List;

  public class VectorIndex {

      public static void main(String[] args) {

          // Replace the placeholder with your Atlas connection string
          String uri = "<connectionString>";

          // Connect to your Atlas cluster
          try (MongoClient mongoClient = MongoClients.create(uri)) {

              // Set the namespace
              MongoDatabase database = mongoClient.getDatabase("sample_mflix");
              MongoCollection<Document> collection = database.getCollection("embedded_movies");

              // Define the index details
              String indexName = "vector_index";
              Bson definition = new Document(
                  "fields",
                  Collections.singletonList(
                      new Document("type", "vector")
                          .append("path", "plot_embedding")
                          .append("numDimensions", 1536)
                          .append("similarity", "dotProduct")
                          .append("quantization", "scalar")));

              // Define the index model
              SearchIndexModel indexModel = new SearchIndexModel(
                  indexName,
                  definition,
                  SearchIndexType.vectorSearch());

              // Create the index using the defined model
              List<String> result = collection.createSearchIndexes(Collections.singletonList(indexModel));
              System.out.println("Successfully created vector index named: " + result.get(0));
              System.out.println("It may take up to a minute for the index to leave the BUILDING status and become queryable.");

              // Wait for Atlas to build the index
              System.out.println("Polling to confirm the index has left the BUILDING status.");
              // No special handling in case of a timeout. Custom handling can be implemented.
              waitForIndex(collection, indexName);
          }
      }

      /**
       * Polls the collection to check whether the specified index is ready to query.
       */
      public static <T> boolean waitForIndex(final MongoCollection<T> collection, final String indexName) {
          long startTime = System.nanoTime();
          long timeoutNanos = TimeUnit.SECONDS.toNanos(60);
          while (System.nanoTime() - startTime < timeoutNanos) {
              Document indexRecord = StreamSupport.stream(collection.listSearchIndexes().spliterator(), false)
                      .filter(index -> indexName.equals(index.getString("name")))
                      .findAny().orElse(null);
              if (indexRecord != null) {
                  if ("FAILED".equals(indexRecord.getString("status"))) {
                      throw new RuntimeException("Search index has FAILED status.");
                  }
                  if (indexRecord.getBoolean("queryable")) {
                      System.out.println(indexName + " index is ready to query");
                      return true;
                  }
              }
              try {
                  Thread.sleep(100); // busy-wait, avoid in production
              } catch (InterruptedException e) {
                  Thread.currentThread().interrupt();
                  throw new RuntimeException(e);
              }
          }
          return false;
      }
  }

  ```

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

  This code also includes a polling mechanism to check if the index is ready to use.

- Specify the `<connection-string>`.

  <Tabs>

  <Tab name="Atlas Cluster">

  Your connection string should use the following format:

  ```
  mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
  ```

  Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

  </Tab>

  <Tab name="Local Deployment">

  Your connection string should use the following format:

  ```
  mongodb://localhost:<port-number>/?directConnection=true
  ```

  </Tab>

  </Tabs>

- Run the file in your IDE, or execute a command from the command line to run the code.

  ```shell
  javac VectorIndex.java
  java VectorIndex
  ```

  ```console
  New search index named vector_index is building.
  Polling to check if the index is ready. This may take up to a minute.
  vector_index is ready for querying.

  ```

</Tab>

<Tab name="Kotlin (Coroutine)">

- Install the MongoDB Kotlin Coroutine Driver.

  For more detailed installation instructions and version compatibility, see the [MongoDB Kotlin Coroutine Driver documentation](https://www.mongodb.com/docs/drivers/kotlin/coroutine/current/quick-start/#std-label-kotlin-quickstart).

- Define the index.

  Create a file named `VectorIndex.kt`. Copy and paste the following code into the file.

  ```kotlin
  import com.mongodb.MongoException
  import com.mongodb.client.model.SearchIndexModel
  import com.mongodb.client.model.SearchIndexType
  import com.mongodb.kotlin.client.coroutine.MongoClient
  import kotlinx.coroutines.delay
  import kotlinx.coroutines.flow.toList
  import org.bson.Document
  import kotlinx.coroutines.runBlocking

  fun main() {
      // Replace the placeholder with your MongoDB deployment's connection string
      val uri = "<connection-string>"
      val mongoClient = MongoClient.create(uri)
      val database = mongoClient.getDatabase("sample_mflix")
      val collection = database.getCollection<Document>("embedded_movies")
      val indexName = "vector_index"
      val searchIndexModel = SearchIndexModel(
          indexName,
          Document(
              "fields",
              listOf(
                  Document("type", "vector")
                      .append("path", "plot_embedding")
                      .append("numDimensions", 1536)
                      .append("similarity", "dotProduct")
                      .append("quantization", "scalar")
              )
          ),
          SearchIndexType.vectorSearch()
      )

      runBlocking {
          try {
              collection.createSearchIndexes(listOf(searchIndexModel))
                  .collect { result ->
                      println("New search index named $result is building.")
                  }

              // Polling to check if the index is queryable
              println("Polling to check if the index is ready. This may take up to a minute.")
              var isQueryable = false
              while (!isQueryable) {
                  delay(5000L) // Poll every 5 seconds

                  val indexes = collection.listSearchIndexes().toList()
                  isQueryable = indexes.any { index ->
                      index.getString("name") == indexName && index.getBoolean("queryable")
                  }
                  if (isQueryable) {
                      println("$indexName is ready for querying.")
                  }
              }
          } catch (me: MongoException) {
              System.err.println("An error occurred: $me")
          }
      }
      mongoClient.close()
  }
  ```

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

  This code also includes a polling mechanism to check if the index is ready to use.

- Specify the `<connection-string>`.

  <Tabs>

  <Tab name="Atlas Cluster">

  Your connection string should use the following format:

  ```
  mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
  ```

  Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

  </Tab>

  <Tab name="Local Deployment">

  Your connection string should use the following format:

  ```
  mongodb://localhost:<port-number>/?directConnection=true
  ```

  </Tab>

  </Tabs>

- Run the `VectorIndex.kt` file in your IDE. The output should resemble the following:

  ```
  New search index named vector_index is building.
  Polling to check if the index is ready. This may take up to a minute.
  vector_index is ready for querying.

  ```

</Tab>

<Tab name="Kotlin (Sync)">

- Install the MongoDB Kotlin Sync Driver.

  For more detailed installation instructions and version compatibility, see the [MongoDB Kotlin Sync Driver documentation](https://www.mongodb.com/docs/languages/kotlin/kotlin-sync-driver/current/get-started/download-and-install/#std-label-kotlin-sync-download-install).

- Define the index.

  Create a file named `VectorIndex.kt`. Copy and paste the following code into the file.

  ```kotlin
  import com.mongodb.MongoException
  import com.mongodb.client.model.SearchIndexModel
  import com.mongodb.client.model.SearchIndexType
  import com.mongodb.kotlin.client.MongoClient
  import org.bson.Document

  fun main() {
      // Replace the placeholder with your MongoDB deployment's connection string
      val uri = "<connection-string>"
      val mongoClient = MongoClient.create(uri)
      val database = mongoClient.getDatabase("sample_mflix")
      val collection = database.getCollection<Document>("embedded_movies")
      val indexName = "vector_index"
      val searchIndexModel = SearchIndexModel(
          indexName,
          Document(
              "fields",
              listOf(
                  Document("type", "vector")
                      .append("path", "plot_embedding")
                      .append("numDimensions", 1536)
                      .append("similarity", "dotProduct")
              )
          ),
          SearchIndexType.vectorSearch()
      )

      try {
          val result = collection.createSearchIndexes(
              listOf(searchIndexModel)
          )
          println("New search index named ${result.get(0)} is building.")

          // Polling to check if the index is queryable
          println("Polling to check if the index is ready. This may take up to a minute.")
          var isQueryable = false
          while (!isQueryable) {
              val results = collection.listSearchIndexes()
              results.forEach { result ->
                  if (result.getString("name") == indexName && result.getBoolean("queryable")) {
                      println("$indexName is ready for querying.")
                      isQueryable = true
                  } else {
                      Thread.sleep(5000) // Poll every 5 seconds
                  }
              }
          }
      } catch (me: MongoException) {
          System.err.println("An error occurred: $me")
      }
      mongoClient.close()
  }

  ```

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

  This code also includes a polling mechanism to check if the index is ready to use.

- Specify the `<connection-string>`.

  <Tabs>

  <Tab name="Atlas Cluster">

  Your connection string should use the following format:

  ```
  mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
  ```

  Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

  </Tab>

  <Tab name="Local Deployment">

  Your connection string should use the following format:

  ```
  mongodb://localhost:<port-number>/?directConnection=true
  ```

  </Tab>

  </Tabs>

- Run the `VectorIndex.kt` file in your IDE. The output should resemble the following:

  ```
  New search index named vector_index is building.
  Polling to check if the index is ready. This may take up to a minute.
  vector_index is ready for querying.

  ```

</Tab>

<Tab name="Node.js">

- Add the MongoDB Node Driver as a dependency in your project:

  ```sh
  npm install mongodb
  ```

  The examples on this page assume your project manages modules as CommonJS modules. If you're using ES modules, instead, you must modify the import syntax.

- Define the index.

  Create a file named `vector-index.js`. Copy and paste the following code into the file.

  ```javascript
  const { MongoClient } = require("mongodb");

  // connect to your Atlas deployment
  const uri =  "<connectionString>";

  const client = new MongoClient(uri);

  async function run() {
     try {
       const database = client.db("sample_mflix");
       const collection = database.collection("embedded_movies");

       // define your Atlas Vector Search index
       const index = {
           name: "vector_index",
           type: "vectorSearch",
           definition: {
             "fields": [
               {
                 "type": "vector",
                 "numDimensions": 1536,
                 "path": "plot_embedding",
                 "similarity": "dotProduct",
                 "quantization": "scalar"
               }
             ]
           }
       }

       // run the helper method
       const result = await collection.createSearchIndex(index);
       console.log(`New search index named ${result} is building.`);

       // wait for the index to be ready to query
       console.log("Polling to check if the index is ready. This may take up to a minute.")
       let isQueryable = false;
       while (!isQueryable) {
         const cursor = collection.listSearchIndexes();
         for await (const index of cursor) {
           if (index.name === result) {
             if (index.queryable) {
               console.log(`${result} is ready for querying.`);
               isQueryable = true;
             } else {
               await new Promise(resolve => setTimeout(resolve, 5000));
             }
           }
         }
       }
     } finally {
       await client.close();
     }
  }
  run().catch(console.dir);

  ```

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

  This code also includes a polling mechanism to check if the index is ready to use.

- Specify the `<connection-string>`.

  <Tabs>

  <Tab name="Atlas Cluster">

  Your connection string should use the following format:

  ```
  mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
  ```

  Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

  </Tab>

  <Tab name="Local Deployment">

  Your connection string should use the following format:

  ```
  mongodb://localhost:<port-number>/?directConnection=true
  ```

  </Tab>

  </Tabs>

- Create the index.

  ```shell
  node vector-index.js
  ```

  ```console
  New search index named vector_index is building.
  Polling to check if the index is ready. This may take up to a minute.
  vector_index is ready for querying.

  ```

</Tab>

<Tab name="PHP">

- Install the MongoDB PHP Driver.

  For detailed installation instructions, see the [MongoDB PHP Library documentation](https://www.mongodb.com/docs/php-library/current/get-started/#std-label-php-download-and-install).

- Define the index.

  Create a file named `vector-index.php`. Copy and paste the following code into the file.

  ```php
  <?php

  require 'vendor/autoload.php';

  // Replace the placeholder with your Atlas connection string
  $uri = "<connection-string>";
  $client = new MongoDB\Client($uri);
  $collection = $client->sample_mflix->embedded_movies;
  $indexName = "vector_index";
  try {
      $result = $collection->createSearchIndexes(
          [[
              'name' => $indexName,
              'type' => 'vectorSearch',
              'definition' => [
                  'fields' => [[
                      'type' => 'vector',
                      'path' => 'plot_embedding',
                      'numDimensions' => 1536,
                      'similarity' => 'dotProduct',
                      'quantization' => 'scalar'
                  ]]
              ],
          ]]
      );
      echo "New search index named $result[0] is building." .PHP_EOL;

      // Polling for the index to become queryable
      echo "Polling to check if the index is ready. This may take up to a minute." .PHP_EOL;
      $isIndexQueryable = false;
      while (!$isIndexQueryable) {
          // List the search indexes
          $searchIndexes = $collection->listSearchIndexes();
          // Check if the index is present and queryable
          foreach ($searchIndexes as $index) {
              if ($index->name === $indexName) {
                  $isIndexQueryable = $index->queryable;
              }
          }
          if (!$isIndexQueryable) {
              sleep(5); // Wait for 5 seconds before polling again
          }
      }
      echo "$indexName is ready for querying." .PHP_EOL;
  }
  catch (MongoDB\Driver\Exception\Exception $e) {
      print 'Error creating the index: ' .$e->getMessage() .PHP_EOL;
  }

  ```

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

  This code also includes a polling mechanism to check if the index is ready to use.

- Specify the `<connection-string>`.

  <Tabs>

  <Tab name="Atlas Cluster">

  Your connection string should use the following format:

  ```
  mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
  ```

  Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

  </Tab>

  <Tab name="Local Deployment">

  Your connection string should use the following format:

  ```
  mongodb://localhost:<port-number>/?directConnection=true
  ```

  </Tab>

  </Tabs>

- Create the index.

  ```shell
  php vector-index.php
  ```

  ```console
  New search index named vector_index is building.
  Polling to check if the index is ready. This may take up to a minute.
  vector_index is ready for querying.

  ```

</Tab>

<Tab name="Python">

- Add the PyMongo Driver as a dependency in your project:

  ```sh
  pip install pymongo
  ```

  For more detailed installation instructions, see the [MongoDB Python Driver documentation](https://www.mongodb.com/docs/languages/python/pymongo-driver/get-started/download-and-install/#std-label-pymongo-get-started-download-and-install).

- Define the index.

  Create a file named `vector-index.py`. Copy and paste the following code into the file.

  ```python
  from pymongo.mongo_client import MongoClient
  from pymongo.operations import SearchIndexModel
  import time

  # Connect to your Atlas deployment
  uri = "<connectionString>"
  client = MongoClient(uri)

  # Access your database and collection
  database = client["sample_mflix"]
  collection = database["embedded_movies"]

  # Create your index model, then create the search index
  search_index_model = SearchIndexModel(
    definition={
      "fields": [
        {
          "type": "vector",
          "path": "plot_embedding",
          "numDimensions": 1536,
          "similarity": "dotProduct",
          "quantization": "scalar"
        }
      ]
    },
    name="vector_index",
    type="vectorSearch"
  )

  result = collection.create_search_index(model=search_index_model)
  print("New search index named " + result + " is building.")

  # Wait for initial sync to complete
  print("Polling to check if the index is ready. This may take up to a minute.")
  predicate=None
  if predicate is None:
    predicate = lambda index: index.get("queryable") is True

  while True:
    indices = list(collection.list_search_indexes(result))
    if len(indices) and predicate(indices[0]):
      break
    time.sleep(5)
  print(result + " is ready for querying.")

  client.close()

  ```

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

  This code also includes a polling mechanism to check if the index is ready to use.

- Specify the `<connection-string>`.

  <Tabs>

  <Tab name="Atlas Cluster">

  Your connection string should use the following format:

  ```
  mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
  ```

  Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

  </Tab>

  <Tab name="Local Deployment">

  Your connection string should use the following format:

  ```
  mongodb://localhost:<port-number>/?directConnection=true
  ```

  </Tab>

  </Tabs>

- Create the index.

  ```shell
  python vector-index.py
  ```

  ```console
  New search index named vector_index is building.
  Polling to check if the index is ready. This may take up to a minute.
  vector_index is ready for querying.

  ```

</Tab>

<Tab name="Ruby">

- Create a file named `vector-index.json`

- Copy and paste the following index definition into the JSON (Javascript Object Notation) file.

  ```
  {
      "database": "sample_mflix",
      "collectionName": "embedded_movies",
      "type": "vectorSearch",
      "name": "vector_index",
      "fields": [
          {
          "type": "vector",
          "path": "plot_embedding",
          "numDimensions": 1536,
          "similarity": "dotProduct"
          }
      ]
  }
  ```

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

- Save the file, and then run the following command in your terminal, replacing `<path-to-file>` with the path to the `vector-index.json` file that you created.

  ```
  atlas deployments search indexes create --file <path-to-file>
  ```

</Tab>

<Tab name="Rust">

- Install the Rust driver for MongoDB.

  For more detailed installation instructions, see the [MongoDB Rust Driver documentation](https://www.mongodb.com/docs/drivers/rust/current/quick-start/download-and-install/#std-label-rust-quick-start-download-and-install).

- Define the index.

  In the `/src` directory of your project, create a file named `vector_index.rs`. Copy and paste the following code into the file.

  <Tabs>

  <Tab name="">

  ```rust
  use std::ops::Index;
  use std::time::Duration;
  use futures::{TryStreamExt};
  use mongodb::{bson::{Document, doc}, Client, Collection, SearchIndexModel};
  use mongodb::SearchIndexType::VectorSearch;
  use tokio::time::sleep;

  #[tokio::main]
  pub(crate) async fn vector_index() {
      // Replace the placeholder with your Atlas connection string
      let uri = "<connection_string>";

      // Create a new client and connect to the server
      let client = Client::with_uri_str(uri).await.unwrap();

      // Get a handle on the movies collection
      let database = client.database("sample_mflix");
      let my_coll: Collection<Document> = database.collection("embedded_movies");

      let index_name = "vector_index";
      let search_index_def = SearchIndexModel::builder()
          .definition(doc! {
              "fields": vec! {doc! {
                  "type": "vector",
                  "path": "plot_embedding",
                  "numDimensions": 1536,
                  "similarity": "dotProduct",
                  "quantization": "scalar"
              }}
          })
          .name(index_name.to_string())
          .index_type(VectorSearch)
          .build();

      let models = vec![search_index_def];
      let result = my_coll.create_search_indexes(models).await;
      if let Err(e) = result {
          eprintln!("There was an error creating the search index: {}", e);
          std::process::exit(1)
      } else {
          println!("New search index named {} is building.", result.unwrap().index(0));
      }

      // Polling for the index to become queryable
      println!("Polling to check if the index is ready. This may take up to a minute.");
      let mut is_index_queryable = false;
      while !is_index_queryable {
          // List the search indexes
          let mut search_indexes = my_coll.list_search_indexes().await.unwrap();
          // Check if the index is present and queryable
          while let Some(index) = search_indexes.try_next().await.unwrap() {
              let retrieved_name = index.get_str("name");
              if retrieved_name.unwrap().to_string() == index_name {
                  is_index_queryable = index.get_bool("queryable").unwrap();
              }
          }
          if !is_index_queryable {
              sleep(Duration::from_secs(5)).await; // Wait for 5 seconds before polling again
          }
      }
      println!("{} is ready for querying.", index_name);
  }

  ```

  </Tab>

  <Tab name="">

  ```rust
  use std::ops::Index;
  use std::time::Duration;
  use std::thread::sleep;
  use mongodb::{
      bson::{doc, Document},
      Client, Collection, SearchIndexModel,
  };
  use mongodb::options::ClientOptions;
  use mongodb::SearchIndexType::VectorSearch;

  pub(crate) fn vector_index() {
      // Replace the placeholder with your Atlas connection string
      let uri = "<connection_string>";

      // Create a new client and connect to the server
      let options = ClientOptions::parse(uri).run().unwrap();
      let client = Client::with_options(options).unwrap();

      // Get a handle on the movies collection
      let database = client.database("sample_mflix");
      let my_coll: Collection<Document> = database.collection("embedded_movies");

      let index_name = "vector_index";
      let search_index_def = SearchIndexModel::builder()
          .definition(doc! {
              "fields": vec! {doc! {
                  "type": "vector",
                  "path": "plot_embedding",
                  "numDimensions": 1536,
                  "similarity": "dotProduct"
              }}
          })
          .name(index_name.to_string())
          .index_type(VectorSearch)
          .build();

      let models = vec![search_index_def];
      let result = my_coll.create_search_indexes(models).run();
      if let Err(e) = result {
          eprintln!("There was an error creating the search index: {}", e);
          std::process::exit(1)
      } else {
          println!("New search index named {} is building.", result.unwrap().index(0));
      }

      // Polling for the index to become queryable
      println!("Polling to check if the index is ready. This may take up to a minute.");
      let mut is_index_queryable = false;
      while !is_index_queryable {
          // List the search indexes
          let search_indexes = my_coll.list_search_indexes().run().unwrap();

          // Check if the index is present and queryable
          for index in search_indexes {
              let unwrapped_index = index.unwrap();
              let retrieved_name = unwrapped_index.get_str("name").unwrap();
              if retrieved_name == index_name {
                  is_index_queryable = unwrapped_index.get_bool("queryable").unwrap_or(false);
              }
          }

          if !is_index_queryable {
              sleep(Duration::from_secs(5)); // Wait for 5 seconds before polling again
          }
      }
      println!("{} is ready for querying.", index_name);
  }

  ```

  </Tab>

  </Tabs>

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

  This code also includes a polling mechanism to check if the index is ready to use.

- Specify the `<connection-string>`.

  <Tabs>

  <Tab name="Atlas Cluster">

  Your connection string should use the following format:

  ```
  mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
  ```

  Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

  </Tab>

  <Tab name="Local Deployment">

  Your connection string should use the following format:

  ```
  mongodb://localhost:<port-number>/?directConnection=true
  ```

  </Tab>

  </Tabs>

- Call the function from your `main.rs`.

  ```rust
  mod vector_index;

  fn main() {
     vector_index::vector_index();
  }
  ```

- Run the file in your IDE, or execute a command from the command line to run the code.

  ```shell
  cargo run
  ```

  ```console
  New search index named vector_index is building.
  Polling to check if the index is ready. This may take up to a minute.
  vector_index is ready for querying.

  ```

</Tab>

<Tab name="Scala">

- Install the MongoDB Scala Driver.

  For installation instructions based on your environment and the version of Scala you are using, refer to the

  [MongoDB Scala Driver documentation](https://www.mongodb.com/docs/languages/scala/scala-driver/current/get-started/#std-label-scala-quick-start-download-and-install).

- Create a new Scala project with the tools you normally use. For this quick start, we create a project named `quick-start`, by using [sbt](https://www.scala-sbt.org).

  ```sh
  sbt new scala/hello-world.g8
  ```

  When prompted, name the application `quick-start`.

- Navigate to your `quick-start` project and create a file named `VectorIndex.scala`. Copy and paste the following code into the file.

  ```scala
  import org.mongodb.scala._
  import org.mongodb.scala.model._
  import com.mongodb.client.model.SearchIndexType

  class VectorIndex {
    def createIndex(): Unit = {
      val collection =
        MongoClient("<connection-string>")
          .getDatabase("sample_mflix")
          .getCollection("embedded_movies")
      val indexName = "vector_index"
      val indexNameAsOption = Option(indexName)
      val indexDefinition = Document(
        "fields" -> List(
          Document(
            "type" -> "vector",
            "path" -> "plot_embedding",
            "numDimensions" -> 1536,
            "similarity" -> "dotProduct",
            "quantization" -> "scalar"
          )
        )
      )
      val indexType = Option(SearchIndexType.vectorSearch())
      val indexModel: SearchIndexModel = SearchIndexModel(
        indexNameAsOption, indexDefinition, indexType
      )
      collection.createSearchIndexes(List(indexModel)).foreach { doc => println(s"New search index named $doc is building.") }
      Thread.sleep(2000)
      println("Polling to check if the index is ready. This may take up to a minute.")
      var indexReady = false
      while (!indexReady) {
        val searchIndexes = collection.listSearchIndexes()
        searchIndexes.foreach { current =>
          val document = current.toBsonDocument()
          val name = document.get("name").asString().getValue
          val queryable = document.get("queryable").asBoolean().getValue
          if (name == indexName && queryable) {
            indexReady = true
          }
        }
        if (!indexReady) {
          Thread.sleep(5000)
        }
      }
      println(s"$indexName is ready for querying.")
    }
  }
  ```

  This index definition:

  - Indexes the `plot_embedding` field as the `vector` [type](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector). This field contains [vector embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector-embeddings) that represent the summary of a movie's plot.

    - Specifies `1536` [vector dimensions](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-term-vector).

    - Measures [similarity](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-fields-similarity) using `dotProduct` similarity.

    - Enables [automatic](https://mongodb.com/docs/atlas/atlas-vector-search/vector-quantization/#std-label-avs-automatic-quantization)
      `quantization` of the vectors.

  This code also includes a polling mechanism to check if the index is ready to use.

- Specify the `<connection-string>`.

  <Tabs>

  <Tab name="Atlas Cluster">

  Your connection string should use the following format:

  ```
  mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
  ```

  Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

  </Tab>

  <Tab name="Local Deployment">

  Your connection string should use the following format:

  ```
  mongodb://localhost:<port-number>/?directConnection=true
  ```

  </Tab>

  </Tabs>

- Create a class instance and call the function in your project's `Main.scala` file.

  ```scala
  object Main extends App {
     private val indexInstance = new VectorIndex
     indexInstance.createIndex()
  }
  ```

- Run the file in your IDE, or execute a command from the command line to run the code.

  There are many tools and environments in which Scala runs. In this example, we run the new Scala project by starting the sbt server with the `sbt` command, then typing `~run`.

  ```shell
  sbt:quick-start> ~run
  ```

  ```console
  New search index named vector_index is building.
  Polling to check if the index is ready. This may take up to a minute.
  vector_index is ready for querying.

  ```

</Tab>

</Tabs>

</Tab>

</Tabs>

## Run a Vector Search Query

In this section, you run a sample vector search query on your indexed embeddings.

<Tabs>

<Tab name="Atlas UI">

### Click the [Collections](https://cloud.mongodb.com/go?l=https%3A%2F%2Fcloud.mongodb.com%2Fv2%2F%3Cproject%3E%23%2Fmetrics%2FreplicaSet%2F%3Creplset%3E%2Fexplorer%2Fsample_mflix%2Fcomments%2Ffind) tab in the Atlas UI.

### Go to the Aggregation tab for the collection.

- Expand `sample_mflix` under the list of databases and select `embedded_movies` from the list of collections in that database.

- Click the Aggregation tab for the `sample_mflix.embedded_movies` collection.

### Construct and run your vector search query.

- In the aggregation pipeline pane, click the </> Text toggle to enable text mode for pipeline editing.

- Copy and paste the following sample query into the text editor.

  This query searches for documents that include text in the `plot` field that is semantically related to the term "time travel".

```json
[
  {
    "$vectorSearch": {
      "index": "vector_index",
      "path": "plot_embedding",
      "queryVector": [-0.0016261312, -0.*********, ...],
      "numCandidates": 150,
      "limit": 10,
      "quantization": "scalar"
    }
  },
  {
    "$project": {
      "_id": 0,
      "plot": 1,
      "title": 1,
      "score": { $meta: "vectorSearchScore" }
    }
  }
]

```

```javascript
{
    "score": 0.9332506656646729,
    "plot": "A reporter, learning of time travelers visiting 20th century disasters, tries to change the history they know by averting upcoming disasters.",
    "title": "Thrill Seekers"
}
{
    "plot": "At the age of 21, Tim discovers he can travel in time and change what happens and has happened in his own life. His decision to make his world a better place by getting a girlfriend turns out not to be as easy as you might think.",
    "title": "About Time",
    "score": 0.9312690496444702
}
{
    "plot": "Hoping to alter the events of the past, a 19th century inventor instead travels 800,000 years into the future, where he finds humankind divided into two warring races.",
    "title": "The Time Machine",
    "score": 0.929530143737793
}
{
    "plot": "After using his mother's newly built time machine, Dolf gets stuck involuntary in the year 1212. He ends up in a children's crusade where he confronts his new friends with modern techniques...",
    "title": "Crusade in Jeans",
    "score": 0.9290417432785034
}
{
    "title": "Timecop",
    "score": 0.9283161759376526,
    "plot": "An officer for a security agency that regulates time travel, must fend for his life against a shady politician who has a tie to his past."
}
{
    "plot": "A time-travel experiment in which a robot probe is sent from the year 2073 to the year 1973 goes terribly wrong thrusting one of the project scientists, a man named Nicholas Sinclair into a...",
    "title": "A.P.E.X.",
    "score": 0.9266218543052673
}
{
    "plot": "Agent J travels in time to M.I.B.'s early days in 1969 to stop an alien from assassinating his friend Agent K and changing history.",
    "title": "Men in Black 3",
    "score": 0.9258455038070679
}
{
    "score": 0.9240515828132629,
    "plot": "Bound by a shared destiny, a teen bursting with scientific curiosity and a former boy-genius inventor embark on a mission to unearth the secrets of a place somewhere in time and space that exists in their collective memory.",
    "title": "Tomorrowland"
}
{
    "plot": "With the help of his uncle, a man travels to the future to try and bring his girlfriend back to life.",
    "title": "Love Story 2050",
    "score": 0.923175573348999
}
{
    "plot": "A dimension-traveling wizard gets stuck in the 21st century because cell-phone radiation interferes with his magic. With his home world on the brink of war, he seeks help from a jaded ...",
    "title": "The Portal",
    "score": 0.9228089451789856
}

```

This query uses the `$vectorSearch` stage to:

- Compare vector embeddings of the search term against vector embeddings of movie plots in the `plot_embedding` field of the `sample_mflix.embedded_movies` collection.

- Consider up to the 150 most similar movie plots and return the top 10 results.

It uses the `$project` stage to:

- Only include the movie `plot` and `title` fields in the results.

- Add a `score` field to show the relevance of each result to the search term.

The Pipeline Output pane displays the results of your query.

To learn more about this pipeline stage, see [Run Vector Search Queries](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-stage/#std-label-return-vector-search-results).

</Tab>

<Tab name="MongoDB Shell">

### Create a file to store your query embeddings.

- Create a file named `query-embeddings.js` to store the query embeddings.

- Copy and paste the following embeddings for the string `time travel` into the `query-embeddings.js` file.

  ```js
  QUERY_EMBEDDING = [-0.0016261312, -0.*********, ...];
  ```

- Save the `query-embeddings.js` file.

### Load the `query-embeddings.js` file in [`mongosh`](https://www.mongodb.com/docs/mongodb-shell/#mongodb-binary-bin.mongosh).

In [`mongosh`](https://www.mongodb.com/docs/mongodb-shell/#mongodb-binary-bin.mongosh), run the following command, replacing `<path-to-file>` with the complete path to your `query-embeddings.js` file.

```shell
load('<path-to-file>/query-embeddings.js')
```

You can verify if the file loaded properly by running the following command:

```shell
QUERY_EMBEDDING.length
```

```shell
1536
```

### Construct and run your vector search query.

Copy and paste the following sample query into your terminal and then run it using [`mongosh`](https://www.mongodb.com/docs/mongodb-shell/#mongodb-binary-bin.mongosh).

This query searches for documents that include text in the `plot` field that is semantically related to the term "time travel".

```json
db.embedded_movies.aggregate([
  {
    "$vectorSearch": {
      "index": "vector_index",
      "path": "plot_embedding",
      "queryVector": QUERY_EMBEDDING,
      "numCandidates": 150,
      "limit": 10,
      "quantization": "scalar"
    }
  },
  {
    "$project": {
      "_id": 0,
      "plot": 1,
      "title": 1,
      "score": { $meta: "vectorSearchScore" }
    }
  }
])

```

```js
[
  {
    plot: 'A reporter, learning of time travelers visiting 20th century disasters, tries to change the history they know by averting upcoming disasters.',
    title: 'Thrill Seekers',
    score: 0.7892671227455139
  },
  {
    plot: 'At the age of 21, Tim discovers he can travel in time and change what happens and has happened in his own life. His decision to make his world a better place by getting a girlfriend turns out not to be as easy as you might think.',
    title: 'About Time',
    score: 0.7843604683876038
  },
  {
    plot: 'Hoping to alter the events of the past, a 19th century inventor instead travels 800,000 years into the future, where he finds humankind divided into two warring races.',
    title: 'The Time Machine',
    score: 0.7801066637039185
  },
  {
    plot: "After using his mother's newly built time machine, Dolf gets stuck involuntary in the year 1212. He ends up in a children's crusade where he confronts his new friends with modern techniques...",
    title: 'Crusade in Jeans',
    score: 0.7789170742034912
  },
  {
    plot: 'An officer for a security agency that regulates time travel, must fend for his life against a shady politician who has a tie to his past.',
    title: 'Timecop',
    score: 0.7771612405776978
  },
  {
    plot: 'A time-travel experiment in which a robot probe is sent from the year 2073 to the year 1973 goes terribly wrong thrusting one of the project scientists, a man named Nicholas Sinclair into a...',
    title: 'A.P.E.X.',
    score: 0.7730885744094849
  },
  {
    plot: "Agent J travels in time to M.I.B.'s early days in 1969 to stop an alien from assassinating his friend Agent K and changing history.",
    title: 'Men in Black 3',
    score: 0.7712380886077881
  },
  {
    plot: 'Bound by a shared destiny, a teen bursting with scientific curiosity and a former boy-genius inventor embark on a mission to unearth the secrets of a place somewhere in time and space that exists in their collective memory.',
    title: 'Tomorrowland',
    score: 0.7669923901557922
  },
  {
    plot: 'With the help of his uncle, a man travels to the future to try and bring his girlfriend back to life.',
    title: 'Love Story 2050',
    score: 0.7649372816085815
  },
  {
    plot: 'A dimension-traveling wizard gets stuck in the 21st century because cell-phone radiation interferes with his magic. With his home world on the brink of war, he seeks help from a jaded ...',
    title: 'The Portal',
    score: 0.7640786170959473
  }
]
```

This query uses the `$vectorSearch` stage to:

- Compare vector embeddings of the search term against vector embeddings of movie plots in the `plot_embedding` field of the `sample_mflix.embedded_movies` collection.

- Consider up to the 150 most similar movie plots and return the top 10 results.

It uses the `$project` stage to:

- Only include the movie `plot` and `title` fields in the results.

- Add a `score` field to show the relevance of each result to the search term.

To learn more about this pipeline stage, see [Run Vector Search Queries](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-stage/#std-label-return-vector-search-results).

</Tab>

<Tab name="C">

### Construct your vector search query.

This query searches for documents that include text in the `plot` field that is semantically related to the term "time travel".

- Create a file named `atlas-vector-search-quick-start.c` and copy and paste the following sample query into the file:

  ```c
  #include <bson/bson.h>
  #include <mongoc/mongoc.h>
  #include <stdio.h>

  int main(void) {

      mongoc_client_t *client;
      mongoc_collection_t *collection;
      mongoc_cursor_t *cursor;
      bson_array_builder_t *builder;
      bson_t *pipeline;
      bson_t query_vector;
      bson_error_t error;
      const bson_t *doc;
      char *str;

      int arrSize = 1536; // This collection has a vector array of 1536 dimensions; depending on your vector embedding model, this mary vary
      double embeddings[] = {-0.0016261312, -0.*********, ...};

      // Initialize the MongoDB C Driver
      mongoc_init();

      // Connect to your Atlas collection
      client =  mongoc_client_new("<connection-string>");
      collection = mongoc_client_get_collection (client, "sample_mflix", "embedded_movies");

      // Build query vector array
      builder = bson_array_builder_new();

      for(int i = 0; i < arrSize; i++){
          bson_array_builder_append_double(builder, embeddings[i]);
      }

      bson_array_builder_build(builder, &query_vector);
      bson_array_builder_destroy(builder);

      // Create aggregation pipeline for Vector Search
      pipeline = BCON_NEW(
          "pipeline",
          "[",
              "{",
                  "$vectorSearch", "{",
                      "index", "vector_index",
                      "path", "plot_embedding",
                      "queryVector", BCON_ARRAY(&query_vector),
                      "numCandidates", BCON_INT32(150),
                      "limit", BCON_INT32(10),
                  "}",
              "}",
              "{",
                  "$project", "{",
                      "title", BCON_BOOL(true),
                      "plot", BCON_BOOL(true),
                      "_id", BCON_BOOL(false),
                      "score", "{",
                          "$meta", "vectorSearchScore",
                      "}",
                  "}",
              "}",
          "]"
      );

      // Run query
      cursor = mongoc_collection_aggregate(collection, MONGOC_QUERY_NONE, pipeline, NULL, NULL );

      // Print results
      while (mongoc_cursor_next (cursor, &doc)) {
          str = bson_as_canonical_extended_json (doc, NULL);
          printf ("%s\n", str);
          bson_free (str);
      }

      // Cleanup
      bson_destroy(pipeline);
      mongoc_cursor_destroy(cursor);
      mongoc_collection_destroy(collection);
      mongoc_client_destroy(client);
      mongoc_cleanup();

      return 0;
  }
  ```

  This query uses the `$vectorSearch` stage to:

  - Compare vector embeddings of the search term against vector embeddings of movie plots in the `plot_embedding` field of the `sample_mflix.embedded_movies` collection.

  - Consider up to the 150 most similar movie plots and return the top 10 results.

  It uses the `$project` stage to:

  - Only include the movie `plot` and `title` fields in the results.

  - Add a `score` field to show the relevance of each result to the search term.

### Specify the `<connection-string>`.

<Tabs>

<Tab name="Atlas Cluster">

Your connection string should use the following format:

```
mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
```

Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

</Tab>

<Tab name="Local Deployment">

Your connection string should use the following format:

```
mongodb://localhost:<port-number>/?directConnection=true
```

</Tab>

</Tabs>

### Update CMake for your project.

- Replace the `vector_index.c` file name in your `CMakeLists.txt` file with your new `atlas-vector-search-quick-start.c` filename.

- Navigate to the `/build` directory, and prepare the project.

  ```bash
  cd build
  cmake ../
  ```

### Run your query.

From your `build` directory, compile and run the `atlas-vector-search-quick-start.c` file.

```shell
cmake --build .
./atlas-vector-search-quick-start
```

```js
{ "plot" : "A reporter, learning of time travelers visiting 20th century disasters, tries to change the history they know by averting upcoming disasters.", "title" : "Thrill Seekers", "score" : { "$numberDouble" : "0.78926712274551391602" } }
{ "plot" : "At the age of 21, Tim discovers he can travel in time and change what happens and has happened in his own life. His decision to make his world a better place by getting a girlfriend turns out not to be as easy as you might think.", "title" : "About Time", "score" : { "$numberDouble" : "0.78436046838760375977" } }
{ "plot" : "Hoping to alter the events of the past, a 19th century inventor instead travels 800,000 years into the future, where he finds humankind divided into two warring races.", "title" : "The Time Machine", "score" : { "$numberDouble" : "0.78010666370391845703" } }
{ "plot" : "After using his mother's newly built time machine, Dolf gets stuck involuntary in the year 1212. He ends up in a children's crusade where he confronts his new friends with modern techniques...", "title" : "Crusade in Jeans", "score" : { "$numberDouble" : "0.77891707420349121094" } }
{ "plot" : "An officer for a security agency that regulates time travel, must fend for his life against a shady politician who has a tie to his past.", "title" : "Timecop", "score" : { "$numberDouble" : "0.77716124057769775391" } }
{ "plot" : "A time-travel experiment in which a robot probe is sent from the year 2073 to the year 1973 goes terribly wrong thrusting one of the project scientists, a man named Nicholas Sinclair into a...", "title" : "A.P.E.X.", "score" : { "$numberDouble" : "0.77308857440948486328" } }
{ "plot" : "Agent J travels in time to M.I.B.'s early days in 1969 to stop an alien from assassinating his friend Agent K and changing history.", "title" : "Men in Black 3", "score" : { "$numberDouble" : "0.77123808860778808594" } }
{ "plot" : "Bound by a shared destiny, a teen bursting with scientific curiosity and a former boy-genius inventor embark on a mission to unearth the secrets of a place somewhere in time and space that exists in their collective memory.", "title" : "Tomorrowland", "score" : { "$numberDouble" : "0.76699239015579223633" } }
{ "plot" : "With the help of his uncle, a man travels to the future to try and bring his girlfriend back to life.", "title" : "Love Story 2050", "score" : { "$numberDouble" : "0.76493728160858154297" } }
{ "plot" : "A dimension-traveling wizard gets stuck in the 21st century because cell-phone radiation interferes with his magic. With his home world on the brink of war, he seeks help from a jaded ...", "title" : "The Portal", "score" : { "$numberDouble" : "0.76407861709594726562" } }
```

</Tab>

<Tab name="C++11">

### Construct your vector search query.

This query searches for documents that include text in the `plot` field that is semantically related to the term "time travel".

- Create a file named `atlas_vector_search_quick_start.cpp` .

- Copy and paste the following sample query into the `atlas_vector_search_quick_start.cpp` file:

  ```cpp
  #include <bsoncxx/builder/basic/document.hpp>
  #include <bsoncxx/builder/list.hpp>
  #include <bsoncxx/json.hpp>
  #include <mongocxx/client.hpp>
  #include <mongocxx/instance.hpp>
  #include <mongocxx/uri.hpp>

  using bsoncxx::builder::basic::kvp;
  using bsoncxx::builder::basic::make_document;

  int main() {
    try {
      mongocxx::instance inst{};

      // Replace the placeholder with your Atlas connection string
      const auto uri = mongocxx::uri{"<connection-string>"};

      // Connect to your Atlas cluster
      auto client = mongocxx::client{uri};
      auto db = client["sample_mflix"];
      auto collection = db["embedded_movies"];

      // Define vector embeddings to search
      auto vectors = bsoncxx::builder::list{
        -0.0016261312, -0.*********, ...
      };

      // Define the pipeline with vectorSearch query options
      mongocxx::pipeline stages;

      stages
          .append_stage(make_document(
              kvp("$vectorSearch",
                  make_document(kvp("index", "vector_index"),
                                kvp("path", "plot_embedding"),
                                kvp("queryVector", vectors),
                                kvp("numCandidates", 150), kvp("limit", 10)))))
          .project(make_document(
              kvp("_id", 0), kvp("plot", 1), kvp("title", 1),
              kvp("score", make_document(kvp("$meta", "vectorSearchScore")))));

      auto cursor = collection.aggregate(stages);

      for (auto&& doc : cursor) {
        std::cout << bsoncxx::to_json(doc) << std::endl;
      }
    } catch (const std::exception& e) {
      std::cout << "Exception: " << e.what() << std::endl;
    }
    return 0;
  }

  ```

This query uses the `$vectorSearch` stage to:

- Compare vector embeddings of the search term against vector embeddings of movie plots in the `plot_embedding` field of the `sample_mflix.embedded_movies` collection.

- Consider up to the 150 most similar movie plots and return the top 10 results.

It uses the `$project` stage to:

- Only include the movie `plot` and `title` fields in the results.

- Add a `score` field to show the relevance of each result to the search term.

To learn more about this pipeline stage, see [Run Vector Search Queries](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-stage/#std-label-return-vector-search-results).

### Specify the `<connection-string>`.

<Tabs>

<Tab name="Atlas Cluster">

Your connection string should use the following format:

```
mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
```

Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

</Tab>

<Tab name="Local Deployment">

Your connection string should use the following format:

```
mongodb://localhost:<port-number>/?directConnection=true
```

</Tab>

</Tabs>

### Compile and run your query.

- Replace the `vector_index.cpp` file name in your `CMakeLists.txt` file with your new `atlas_vector_search_quick_start.cpp` filename.

- Navigate to the `/build` directory, and prepare the project.

  ```bash
  cmake ../
  ```

- Build the app.

  ```bash
  cmake --build .
  ```

- Execute the app to run the query.

  ```bash
  ./query_quick_start
  ```

  ```js
  '{"plot":"A reporter, learning of time travelers visiting 20th century disasters, tries to change the history they know by averting upcoming disasters.","title":"Thrill Seekers","score":0.7892671227455139}'
  '{"plot":"At the age of 21, Tim discovers he can travel in time and change what happens and has happened in his own life. His decision to make his world a better place by getting a girlfriend turns out not to be as easy as you might think.","title":"About Time","score":0.7843604683876038}'
  '{"plot":"Hoping to alter the events of the past, a 19th century inventor instead travels 800,000 years into the future, where he finds humankind divided into two warring races.","title":"The Time Machine","score":0.7801066637039185}'
  `{"plot":"After using his mother's newly built time machine, Dolf gets stuck involuntary in the year 1212. He ends up in a children's crusade where he confronts his new friends with modern techniques...","title":"Crusade in Jeans","score":0.7789170742034912}`
  '{"plot":"An officer for a security agency that regulates time travel, must fend for his life against a shady politician who has a tie to his past.","title":"Timecop","score":0.7771612405776978}'
  '{"plot":"A time-travel experiment in which a robot probe is sent from the year 2073 to the year 1973 goes terribly wrong thrusting one of the project scientists, a man named Nicholas Sinclair into a...","title":"A.P.E.X.","score":0.7730885744094849}'
  `{"plot":"Agent J travels in time to M.I.B.'s early days in 1969 to stop an alien from assassinating his friend Agent K and changing history.","title":"Men in Black 3","score":0.7712380886077881}`
  '{"plot":"Bound by a shared destiny, a teen bursting with scientific curiosity and a former boy-genius inventor embark on a mission to unearth the secrets of a place somewhere in time and space that exists in their collective memory.","title":"Tomorrowland","score":0.7669923901557922}'
  '{"plot":"With the help of his uncle, a man travels to the future to try and bring his girlfriend back to life.","title":"Love Story 2050","score":0.7649372816085815}'
  '{"plot":"A dimension-traveling wizard gets stuck in the 21st century because cell-phone radiation interferes with his magic. With his home world on the brink of war, he seeks help from a jaded ...","title":"The Portal","score":0.7640786170959473}'

  ```

</Tab>

<Tab name="C#">

### Construct your vector search query.

This query searches for documents that include text in the `plot` field that is semantically related to the term "time travel".

- Create a new file called `DatabaseService.cs`.

- Copy and paste the following sample query into the `DatabaseService.cs` file:

  ```csharp
  namespace query_quick_start;

  using MongoDB.Bson;
  using MongoDB.Bson.Serialization.Attributes;
  using MongoDB.Bson.Serialization.Conventions;
  using MongoDB.Driver;

  public class DatabaseService
  {
    // Replace the placeholder with your Atlas connection string
    private const string MongoConnectionString = "<connection-string>";

    public void RunVectorQuery(){
      var camelCaseConvention = new ConventionPack { new CamelCaseElementNameConvention() };
      ConventionRegistry.Register("CamelCase", camelCaseConvention, type => true);

      // connect to your Atlas cluster
      var mongoClient = new MongoClient(MongoConnectionString);

      // define namespace
      var moviesDatabase = mongoClient.GetDatabase("sample_mflix");
      var moviesCollection = moviesDatabase.GetCollection<EmbeddedMovie>("embedded_movies");

      // define vector embeddings to search
      var vector = new[] {-0.0016261312, -0.*********, ...};
      var options = new VectorSearchOptions<EmbeddedMovie>() {
          IndexName = "vector_index",
          NumberOfCandidates = 150
      };

      // run query
      var results = moviesCollection.Aggregate()
                  .VectorSearch(movie => movie.Embedding, vector, 10, options)
                  .Project(Builders<EmbeddedMovie>.Projection
                    .Include(movie => movie.Title)
                    .Include(movie => movie.Plot)
                    .Exclude(movie => movie.Id)
                    .MetaVectorSearchScore(movie => movie.Score))
                  .ToList();

      // print results
      foreach (var movie in results)
        {
          Console.WriteLine(movie.ToJson());
        }
    }
  }

  [BsonIgnoreExtraElements]
  public class EmbeddedMovie
  {
      [BsonIgnoreIfDefault]
      public ObjectId Id { get; set; }
      public string? Title { get; set; }
      public string? Plot { get; set; }
      [BsonElement("plot_embedding")]
      public double[]? Embedding { get; set; }
      public double Score { get; set; }
  }
  ```

This query uses the `$vectorSearch` stage to:

- Compare vector embeddings of the search term against vector embeddings of movie plots in the `plot_embedding` field of the `sample_mflix.embedded_movies` collection.

- Consider up to the 150 most similar movie plots and return the top 10 results.

It uses the `$project` stage to:

- Only include the movie `plot` and `title` fields in the results.

- Add a `score` field to show the relevance of each result to the search term.

To learn more about this pipeline stage, see [Run Vector Search Queries](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-stage/#std-label-return-vector-search-results).

### Specify the `<connection-string>`.

<Tabs>

<Tab name="Atlas Cluster">

Your connection string should use the following format:

```
mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
```

Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

</Tab>

<Tab name="Local Deployment">

Your connection string should use the following format:

```
mongodb://localhost:<port-number>/?directConnection=true
```

</Tab>

</Tabs>

### Update your `Program.cs` file.

Remove the index creation code from your `Program.cs` file. Instead, initialize the `DatabaseService` object and call the method to run the query:

```csharp
using query_quick_start;

var databaseService = new DatabaseService();
databaseService.RunVectorQuery();
```

### Run your query.

Compile and run your project.

```shell
dotnet run query-quick-start.csproj
```

```js
{ "plot" : "A reporter, learning of time travelers visiting 20th century disasters, tries to change the history they know by averting upcoming disasters.", "title" : "Thrill Seekers", "score" : 0.78926712274551392 }
{ "plot" : "At the age of 21, Tim discovers he can travel in time and change what happens and has happened in his own life. His decision to make his world a better place by getting a girlfriend turns out not to be as easy as you might think.", "title" : "About Time", "score" : 0.78436046838760376 }
{ "plot" : "Hoping to alter the events of the past, a 19th century inventor instead travels 800,000 years into the future, where he finds humankind divided into two warring races.", "title" : "The Time Machine", "score" : 0.78010666370391846 }
{ "plot" : "After using his mother's newly built time machine, Dolf gets stuck involuntary in the year 1212. He ends up in a children's crusade where he confronts his new friends with modern techniques...", "title" : "Crusade in Jeans", "score" : 0.77891707420349121 }
{ "plot" : "An officer for a security agency that regulates time travel, must fend for his life against a shady politician who has a tie to his past.", "title" : "Timecop", "score" : 0.77716124057769775 }
{ "plot" : "A time-travel experiment in which a robot probe is sent from the year 2073 to the year 1973 goes terribly wrong thrusting one of the project scientists, a man named Nicholas Sinclair into a...", "title" : "A.P.E.X.", "score" : 0.77308857440948486 }
{ "plot" : "Agent J travels in time to M.I.B.'s early days in 1969 to stop an alien from assassinating his friend Agent K and changing history.", "title" : "Men in Black 3", "score" : 0.77123808860778809 }
{ "plot" : "Bound by a shared destiny, a teen bursting with scientific curiosity and a former boy-genius inventor embark on a mission to unearth the secrets of a place somewhere in time and space that exists in their collective memory.", "title" : "Tomorrowland", "score" : 0.76699239015579224 }
{ "plot" : "With the help of his uncle, a man travels to the future to try and bring his girlfriend back to life.", "title" : "Love Story 2050", "score" : 0.76493728160858154 }
{ "plot" : "A dimension-traveling wizard gets stuck in the 21st century because cell-phone radiation interferes with his magic. With his home world on the brink of war, he seeks help from a jaded ...", "title" : "The Portal", "score" : 0.76407861709594727 }

```

</Tab>

<Tab name="Go">

### Construct your vector search query.

This query searches for documents that include text in the `plot` field that is semantically related to the term "time travel".

- Create a file named `atlas_vector_search_quick_start.go` .

- Copy and paste the following sample query into the `atlas_vector_search_quick_start.go` file:

  ```go
  package main

  import (
  	"context"
  	"encoding/json"
  	"fmt"
  	"log"

  	"go.mongodb.org/mongo-driver/v2/bson"
  	"go.mongodb.org/mongo-driver/v2/mongo"
  	"go.mongodb.org/mongo-driver/v2/mongo/options"
  )

  func main() {
  	ctx := context.Background()

  	// Replace the placeholder with your Atlas connection string
  	const uri = "<connection-string>"

  	// Connect to your Atlas cluster
  	clientOptions := options.Client().ApplyURI(uri)
  	client, err := mongo.Connect(clientOptions)
  	if err != nil {
  		log.Fatalf("failed to connect to the server: %v", err)
  	}
  	defer func() { _ = client.Disconnect(ctx) }()

  	// Set the namespace
  	coll := client.Database("sample_mflix").Collection("embedded_movies")

  	// Define the pipeline with $vectorSearch query options
  	queryVectorValues := []float64{
  		-0.0016261312, -0.*********, ...,
  	}

  	pipeline := mongo.Pipeline{
  		bson.D{
  			{"$vectorSearch", bson.D{
  				{"queryVector", queryVectorValues},
  				{"index", "vector_index"},
  				{"path", "plot_embedding"},
  				{"numCandidates", 150},
  				{"limit", 10},
  			}},
  		},
  		bson.D{
  			{"$project", bson.D{
  				{"_id", 0},
  				{"plot", 1},
  				{"title", 1},
  				{"score", bson.D{
  					{"$meta", "vectorSearchScore"},
  				}},
  			}},
  		},
  	}

  	// Run the pipeline
  	cursor, err := coll.Aggregate(ctx, pipeline)
  	if err != nil {
  		log.Fatalf("Failed to run aggregation: %v", err)
  	}
  	defer cursor.Close(ctx)

  	// Print results
  	for cursor.Next(ctx) {
  		var doc bson.M
  		if err := cursor.Decode(&doc); err != nil {
  			log.Fatalf("Failed to decode document: %v", err)
  		}
  		jsonDoc, err := json.Marshal(doc)
  		if err != nil {
  			log.Fatalf("Failed to marshal document to JSON: %v", err)
  		}
  		fmt.Println(string(jsonDoc))
  	}

  	if err := cursor.Err(); err != nil {
  		log.Fatalf("Cursor error: %v", err)
  	}
  }

  ```

This query uses the `$vectorSearch` stage to:

- Compare vector embeddings of the search term against vector embeddings of movie plots in the `plot_embedding` field of the `sample_mflix.embedded_movies` collection.

- Consider up to the 150 most similar movie plots and return the top 10 results.

It uses the `$project` stage to:

- Only include the movie `plot` and `title` fields in the results.

- Add a `score` field to show the relevance of each result to the search term.

To learn more about this pipeline stage, see [Run Vector Search Queries](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-stage/#std-label-return-vector-search-results).

### Specify the `<connection-string>`.

<Tabs>

<Tab name="Atlas Cluster">

Your connection string should use the following format:

```
mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
```

Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

</Tab>

<Tab name="Local Deployment">

Your connection string should use the following format:

```
mongodb://localhost:<port-number>/?directConnection=true
```

</Tab>

</Tabs>

### Run your query.

Run the following command to query your collection:

```bash
go run atlas_vector_search_quick_start.go
```

```js
'{"plot":"A reporter, learning of time travelers visiting 20th century disasters, tries to change the history they know by averting upcoming disasters.","title":"Thrill Seekers","score":0.7892671227455139}'
'{"plot":"At the age of 21, Tim discovers he can travel in time and change what happens and has happened in his own life. His decision to make his world a better place by getting a girlfriend turns out not to be as easy as you might think.","title":"About Time","score":0.7843604683876038}'
'{"plot":"Hoping to alter the events of the past, a 19th century inventor instead travels 800,000 years into the future, where he finds humankind divided into two warring races.","title":"The Time Machine","score":0.7801066637039185}'
`{"plot":"After using his mother's newly built time machine, Dolf gets stuck involuntary in the year 1212. He ends up in a children's crusade where he confronts his new friends with modern techniques...","title":"Crusade in Jeans","score":0.7789170742034912}`
'{"plot":"An officer for a security agency that regulates time travel, must fend for his life against a shady politician who has a tie to his past.","title":"Timecop","score":0.7771612405776978}'
'{"plot":"A time-travel experiment in which a robot probe is sent from the year 2073 to the year 1973 goes terribly wrong thrusting one of the project scientists, a man named Nicholas Sinclair into a...","title":"A.P.E.X.","score":0.7730885744094849}'
`{"plot":"Agent J travels in time to M.I.B.'s early days in 1969 to stop an alien from assassinating his friend Agent K and changing history.","title":"Men in Black 3","score":0.7712380886077881}`
'{"plot":"Bound by a shared destiny, a teen bursting with scientific curiosity and a former boy-genius inventor embark on a mission to unearth the secrets of a place somewhere in time and space that exists in their collective memory.","title":"Tomorrowland","score":0.7669923901557922}'
'{"plot":"With the help of his uncle, a man travels to the future to try and bring his girlfriend back to life.","title":"Love Story 2050","score":0.7649372816085815}'
'{"plot":"A dimension-traveling wizard gets stuck in the 21st century because cell-phone radiation interferes with his magic. With his home world on the brink of war, he seeks help from a jaded ...","title":"The Portal","score":0.7640786170959473}'

```

</Tab>

<Tab name="Java (Sync)">

### Construct your vector search query.

This query searches for documents that include text in the `plot` field that is semantically related to the term "time travel".

- Create a file named `BasicQuery.java`.

- Copy and paste the following sample query into the `BasicQuery.java` file:

  ```java
  import com.mongodb.client.MongoClient;
  import com.mongodb.client.MongoClients;
  import com.mongodb.client.MongoCollection;
  import com.mongodb.client.MongoDatabase;
  import com.mongodb.client.model.search.FieldSearchPath;
  import org.bson.Document;
  import org.bson.conversions.Bson;

  import java.util.List;

  import static com.mongodb.client.model.Aggregates.project;
  import static com.mongodb.client.model.Aggregates.vectorSearch;
  import static com.mongodb.client.model.Projections.fields;
  import static com.mongodb.client.model.Projections.include;
  import static com.mongodb.client.model.Projections.exclude;
  import static com.mongodb.client.model.Projections.metaVectorSearchScore;
  import static com.mongodb.client.model.search.SearchPath.fieldPath;
  import static com.mongodb.client.model.search.VectorSearchOptions.approximateVectorSearchOptions;
  import static java.util.Arrays.asList;

  public class BasicQuery {
    public static void main( String[] args ) {
      // specify connection
      String uri = "<connection-string>";

      // establish connection and set namespace
      try (MongoClient mongoClient = MongoClients.create(uri)) {
        MongoDatabase database = mongoClient.getDatabase("sample_mflix");
        MongoCollection<Document> collection = database.getCollection("embedded_movies");

        // define $vectorSearch query options
        List<Double> queryVector = (asList(-0.0016261312d, -0.*********d, ...));
        String indexName = "vector_index";
        FieldSearchPath fieldSearchPath = fieldPath("plot_embedding");
        int limit = 10;
        int numCandidates = 150;

        // define pipeline
        List<Bson> pipeline = asList(
          vectorSearch(
            fieldSearchPath,
            queryVector,
            indexName,
            limit,
            approximateVectorSearchOptions(numCandidates)),
          project(
            fields(exclude("_id"), include("title"), include("plot"),
            metaVectorSearchScore("score"))));

        // run query and print results
        collection.aggregate(pipeline)
          .forEach(doc -> System.out.println(doc.toJson()));	
      }
    }
  }

  ```

This query uses the `$vectorSearch` stage to:

- Compare vector embeddings of the search term against vector embeddings of movie plots in the `plot_embedding` field of the `sample_mflix.embedded_movies` collection.

- Consider up to the 150 most similar movie plots and return the top 10 results.

It uses the `$project` stage to:

- Only include the movie `plot` and `title` fields in the results.

- Add a `score` field to show the relevance of each result to the search term.

To learn more about this pipeline stage, see [Run Vector Search Queries](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-stage/#std-label-return-vector-search-results).

### Specify the `<connection-string>`.

<Tabs>

<Tab name="Atlas Cluster">

Your connection string should use the following format:

```
mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
```

Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

</Tab>

<Tab name="Local Deployment">

Your connection string should use the following format:

```
mongodb://localhost:<port-number>/?directConnection=true
```

</Tab>

</Tabs>

### Run your query.

Compile and run the `BasicQuery.java` file:

```shell
javac BasicQuery.java
java BasicQuery
```

```js
{"plot": "A reporter, learning of time travelers visiting 20th century disasters, tries to change the history they know by averting upcoming disasters.", "title": "Thrill Seekers", "score": 0.7892671227455139}
{"plot": "At the age of 21, Tim discovers he can travel in time and change what happens and has happened in his own life. His decision to make his world a better place by getting a girlfriend turns out not to be as easy as you might think.", "title": "About Time", "score": 0.7843604683876038}
{"plot": "Hoping to alter the events of the past, a 19th century inventor instead travels 800,000 years into the future, where he finds humankind divided into two warring races.", "title": "The Time Machine", "score": 0.7801066637039185}
{"plot": "After using his mother's newly built time machine, Dolf gets stuck involuntary in the year 1212. He ends up in a children's crusade where he confronts his new friends with modern techniques...", "title": "Crusade in Jeans", "score": 0.7789170742034912}
{"plot": "An officer for a security agency that regulates time travel, must fend for his life against a shady politician who has a tie to his past.", "title": "Timecop", "score": 0.7771612405776978}
{"plot": "A time-travel experiment in which a robot probe is sent from the year 2073 to the year 1973 goes terribly wrong thrusting one of the project scientists, a man named Nicholas Sinclair into a...", "title": "A.P.E.X.", "score": 0.7730885744094849}
{"plot": "Agent J travels in time to M.I.B.'s early days in 1969 to stop an alien from assassinating his friend Agent K and changing history.", "title": "Men in Black 3", "score": 0.7712380886077881}
{"plot": "Bound by a shared destiny, a teen bursting with scientific curiosity and a former boy-genius inventor embark on a mission to unearth the secrets of a place somewhere in time and space that exists in their collective memory.", "title": "Tomorrowland", "score": 0.7669923901557922}
{"plot": "With the help of his uncle, a man travels to the future to try and bring his girlfriend back to life.", "title": "Love Story 2050", "score": 0.7649372816085815}
{"plot": "A dimension-traveling wizard gets stuck in the 21st century because cell-phone radiation interferes with his magic. With his home world on the brink of war, he seeks help from a jaded ...", "title": "The Portal", "score": 0.7640786170959473}

```

</Tab>

<Tab name="Kotlin (Coroutine)">

### Construct your vector search query.

This query searches for documents that include text in the `plot` field that is semantically related to the term "time travel".

- Create a file named `atlasVectorSearchQuery.kt`.

- Copy and paste the following sample query into the `atlasVectorSearchQuery.kt` file:

  ```kotlin
  import com.mongodb.client.model.Aggregates
  import com.mongodb.client.model.Projections
  import com.mongodb.client.model.search.SearchPath.fieldPath
  import com.mongodb.client.model.search.VectorSearchOptions
  import com.mongodb.kotlin.client.coroutine.MongoClient
  import kotlinx.coroutines.flow.count
  import org.bson.Document

  data class Movie(val title: String, val plot: String, val score: Double?)

  suspend fun main() {

      // Replace the placeholder with your Atlas connection string
      val uri = "<connection-string>"
      val client = MongoClient.create(uri)

      try {
          // Connect to your Atlas cluster
          val database = client.getDatabase("sample_mflix")
          val collection = database.getCollection<Movie>("embedded_movies")

          // Define the pipeline with $vectorSearch query options
          val queryVector = listOf(
              -0.0016261312, -0.*********, ...,
              )
          val path = fieldPath("plot_embedding")
          val index = "vector_index"
          val limit = 10L
          val numCandidates = 150L

          val pipeline = listOf(
              Aggregates.vectorSearch(
                  path,
                  queryVector,
                  index,
                  limit,
                  VectorSearchOptions.approximateVectorSearchOptions(numCandidates)
              ),
              Aggregates.project(
                  Projections.fields(
                      Projections.excludeId(),
                      Projections.include(Movie::plot.name),
                      Projections.include(Movie::title.name),
                      Projections.metaVectorSearchScore(Movie::score.name)
                  )
              )
          )

          // Run the pipeline
          val resultsFlow = collection.aggregate<Document>(pipeline)

          // Print results
          val resultCount = resultsFlow.count()
          if (resultCount > 0) {
              resultsFlow.collect { result ->
                  println(result.toJson())
              }
          } else {
              println("No results found.")
          }
      } catch (e: Exception) {
          println("Error occurred: ${e.message}")
      } finally {
          client.close()
      }
  }

  ```

This query uses the `$vectorSearch` stage to:

- Compare vector embeddings of the search term against vector embeddings of movie plots in the `plot_embedding` field of the `sample_mflix.embedded_movies` collection.

- Consider up to the 150 most similar movie plots and return the top 10 results.

It uses the `$project` stage to:

- Only include the movie `plot` and `title` fields in the results.

- Add a `score` field to show the relevance of each result to the search term.

To learn more about this pipeline stage, see [Run Vector Search Queries](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-stage/#std-label-return-vector-search-results).

### Specify the `<connection-string>`.

<Tabs>

<Tab name="Atlas Cluster">

Your connection string should use the following format:

```
mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
```

Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

</Tab>

<Tab name="Local Deployment">

Your connection string should use the following format:

```
mongodb://localhost:<port-number>/?directConnection=true
```

</Tab>

</Tabs>

### Run your query.

Run the `atlasVectorSearchQuery.kt` file in your IDE. The output should resemble the following:

```js
'{"plot":"A reporter, learning of time travelers visiting 20th century disasters, tries to change the history they know by averting upcoming disasters.","title":"Thrill Seekers","score":0.7892671227455139}'
'{"plot":"At the age of 21, Tim discovers he can travel in time and change what happens and has happened in his own life. His decision to make his world a better place by getting a girlfriend turns out not to be as easy as you might think.","title":"About Time","score":0.7843604683876038}'
'{"plot":"Hoping to alter the events of the past, a 19th century inventor instead travels 800,000 years into the future, where he finds humankind divided into two warring races.","title":"The Time Machine","score":0.7801066637039185}'
`{"plot":"After using his mother's newly built time machine, Dolf gets stuck involuntary in the year 1212. He ends up in a children's crusade where he confronts his new friends with modern techniques...","title":"Crusade in Jeans","score":0.7789170742034912}`
'{"plot":"An officer for a security agency that regulates time travel, must fend for his life against a shady politician who has a tie to his past.","title":"Timecop","score":0.7771612405776978}'
'{"plot":"A time-travel experiment in which a robot probe is sent from the year 2073 to the year 1973 goes terribly wrong thrusting one of the project scientists, a man named Nicholas Sinclair into a...","title":"A.P.E.X.","score":0.7730885744094849}'
`{"plot":"Agent J travels in time to M.I.B.'s early days in 1969 to stop an alien from assassinating his friend Agent K and changing history.","title":"Men in Black 3","score":0.7712380886077881}`
'{"plot":"Bound by a shared destiny, a teen bursting with scientific curiosity and a former boy-genius inventor embark on a mission to unearth the secrets of a place somewhere in time and space that exists in their collective memory.","title":"Tomorrowland","score":0.7669923901557922}'
'{"plot":"With the help of his uncle, a man travels to the future to try and bring his girlfriend back to life.","title":"Love Story 2050","score":0.7649372816085815}'
'{"plot":"A dimension-traveling wizard gets stuck in the 21st century because cell-phone radiation interferes with his magic. With his home world on the brink of war, he seeks help from a jaded ...","title":"The Portal","score":0.7640786170959473}'

```

</Tab>

<Tab name="Kotlin (Sync)">

### Construct your vector search query.

This query searches for documents that include text in the `plot` field that is semantically related to the term "time travel".

- Create a file named `atlasVectorSearchQuery.kt`.

- Copy and paste the following sample query into the `atlasVectorSearchQuery.kt` file:

  ```kotlin
  import com.mongodb.client.model.Aggregates
  import com.mongodb.client.model.Projections
  import com.mongodb.client.model.search.SearchPath.fieldPath
  import com.mongodb.client.model.search.VectorSearchOptions
  import com.mongodb.kotlin.client.MongoClient
  import org.bson.Document

  data class Movie(val title: String, val plot: String, val score: Double?)

  fun main() {

      // Replace the placeholder with your Atlas connection string
      val uri = "<connection-string>"
      val client = MongoClient.create(uri)

      try {
          // Connect to your Atlas cluster
          val database = client.getDatabase("sample_mflix")
          val collection = database.getCollection<Movie>("embedded_movies")

          // Define the pipeline with $vectorSearch query options
          val queryVector = listOf(
              -0.0016261312, -0.*********, ...,
              )
          val path = fieldPath("plot_embedding")
          val index = "vector_index"
          val limit = 10L
          val numCandidates = 150L

          val pipeline = listOf(
              Aggregates.vectorSearch(
                  path,
                  queryVector,
                  index,
                  limit,
                  VectorSearchOptions.approximateVectorSearchOptions(numCandidates)
              ),
              Aggregates.project(
                  Projections.fields(
                      Projections.excludeId(),
                      Projections.include(Movie::plot.name),
                      Projections.include(Movie::title.name),
                      Projections.metaVectorSearchScore(Movie::score.name)
                  )
              )
          )

          // Run the pipeline
          val results = collection.aggregate<Document>(pipeline).toList()

          // Print results
          if (results.isNotEmpty()) {
              results.forEach { result ->
                  println(result.toJson()).toString()
              }
          } else {
              println("No results found.")
          }
      } catch (e: Exception) {
          println("Error occurred: ${e.message}")
      } finally {
          client.close()
      }
  }

  ```

This query uses the `$vectorSearch` stage to:

- Compare vector embeddings of the search term against vector embeddings of movie plots in the `plot_embedding` field of the `sample_mflix.embedded_movies` collection.

- Consider up to the 150 most similar movie plots and return the top 10 results.

It uses the `$project` stage to:

- Only include the movie `plot` and `title` fields in the results.

- Add a `score` field to show the relevance of each result to the search term.

To learn more about this pipeline stage, see [Run Vector Search Queries](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-stage/#std-label-return-vector-search-results).

### Specify the `<connection-string>`.

<Tabs>

<Tab name="Atlas Cluster">

Your connection string should use the following format:

```
mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
```

Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

</Tab>

<Tab name="Local Deployment">

Your connection string should use the following format:

```
mongodb://localhost:<port-number>/?directConnection=true
```

</Tab>

</Tabs>

### Run your query.

Run the `atlasVectorSearchQuery.kt` file in your IDE. The output should resemble the following:

```js
'{"plot":"A reporter, learning of time travelers visiting 20th century disasters, tries to change the history they know by averting upcoming disasters.","title":"Thrill Seekers","score":0.7892671227455139}'
'{"plot":"At the age of 21, Tim discovers he can travel in time and change what happens and has happened in his own life. His decision to make his world a better place by getting a girlfriend turns out not to be as easy as you might think.","title":"About Time","score":0.7843604683876038}'
'{"plot":"Hoping to alter the events of the past, a 19th century inventor instead travels 800,000 years into the future, where he finds humankind divided into two warring races.","title":"The Time Machine","score":0.7801066637039185}'
`{"plot":"After using his mother's newly built time machine, Dolf gets stuck involuntary in the year 1212. He ends up in a children's crusade where he confronts his new friends with modern techniques...","title":"Crusade in Jeans","score":0.7789170742034912}`
'{"plot":"An officer for a security agency that regulates time travel, must fend for his life against a shady politician who has a tie to his past.","title":"Timecop","score":0.7771612405776978}'
'{"plot":"A time-travel experiment in which a robot probe is sent from the year 2073 to the year 1973 goes terribly wrong thrusting one of the project scientists, a man named Nicholas Sinclair into a...","title":"A.P.E.X.","score":0.7730885744094849}'
`{"plot":"Agent J travels in time to M.I.B.'s early days in 1969 to stop an alien from assassinating his friend Agent K and changing history.","title":"Men in Black 3","score":0.7712380886077881}`
'{"plot":"Bound by a shared destiny, a teen bursting with scientific curiosity and a former boy-genius inventor embark on a mission to unearth the secrets of a place somewhere in time and space that exists in their collective memory.","title":"Tomorrowland","score":0.7669923901557922}'
'{"plot":"With the help of his uncle, a man travels to the future to try and bring his girlfriend back to life.","title":"Love Story 2050","score":0.7649372816085815}'
'{"plot":"A dimension-traveling wizard gets stuck in the 21st century because cell-phone radiation interferes with his magic. With his home world on the brink of war, he seeks help from a jaded ...","title":"The Portal","score":0.7640786170959473}'

```

</Tab>

<Tab name="Node.js">

### Construct your vector search query.

This query searches for documents that include text in the `plot` field that is semantically related to the term "time travel".

- Create a file named `atlas-vector-search-quick-start.js` .

- Copy and paste the following sample query into the `atlas-vector-search-quick-start.js` file:

  ```js
  const { MongoClient } = require("mongodb");

  // connect to your Atlas cluster
  const uri = "<connection-string>";

  const client = new MongoClient(uri);

  async function run() {
      try {
          await client.connect();

          // set namespace
          const database = client.db("sample_mflix");
          const coll = database.collection("embedded_movies");

          // define pipeline
          const agg = [
              {
                '$vectorSearch': {
                  'index': 'vector_index',
                  'path': 'plot_embedding',
                  'queryVector': [
                    -0.0016261312, -0.*********, ...
                  ],
                  'numCandidates': 150,
                  'limit': 10
                }
              }, {
                '$project': {
                  '_id': 0,
                  'plot': 1,
                  'title': 1,
                  'score': {
                    '$meta': 'vectorSearchScore'
                  }
                }
              }
            ];
          // run pipeline
          const result = coll.aggregate(agg);

          // print results
          await result.forEach((doc) => console.dir(JSON.stringify(doc)));
      } finally {
          await client.close();
      }
  }
  run().catch(console.dir);

  ```

This query uses the `$vectorSearch` stage to:

- Compare vector embeddings of the search term against vector embeddings of movie plots in the `plot_embedding` field of the `sample_mflix.embedded_movies` collection.

- Consider up to the 150 most similar movie plots and return the top 10 results.

It uses the `$project` stage to:

- Only include the movie `plot` and `title` fields in the results.

- Add a `score` field to show the relevance of each result to the search term.

To learn more about this pipeline stage, see [Run Vector Search Queries](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-stage/#std-label-return-vector-search-results).

### Specify the `<connection-string>`.

<Tabs>

<Tab name="Atlas Cluster">

Your connection string should use the following format:

```
mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
```

Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

</Tab>

<Tab name="Local Deployment">

Your connection string should use the following format:

```
mongodb://localhost:<port-number>/?directConnection=true
```

</Tab>

</Tabs>

### Run your query.

Run the following command to query your collection:

```bash
node atlas-vector-search-quick-start.js
```

```js
'{"plot":"A reporter, learning of time travelers visiting 20th century disasters, tries to change the history they know by averting upcoming disasters.","title":"Thrill Seekers","score":0.7892671227455139}'
'{"plot":"At the age of 21, Tim discovers he can travel in time and change what happens and has happened in his own life. His decision to make his world a better place by getting a girlfriend turns out not to be as easy as you might think.","title":"About Time","score":0.7843604683876038}'
'{"plot":"Hoping to alter the events of the past, a 19th century inventor instead travels 800,000 years into the future, where he finds humankind divided into two warring races.","title":"The Time Machine","score":0.7801066637039185}'
`{"plot":"After using his mother's newly built time machine, Dolf gets stuck involuntary in the year 1212. He ends up in a children's crusade where he confronts his new friends with modern techniques...","title":"Crusade in Jeans","score":0.7789170742034912}`
'{"plot":"An officer for a security agency that regulates time travel, must fend for his life against a shady politician who has a tie to his past.","title":"Timecop","score":0.7771612405776978}'
'{"plot":"A time-travel experiment in which a robot probe is sent from the year 2073 to the year 1973 goes terribly wrong thrusting one of the project scientists, a man named Nicholas Sinclair into a...","title":"A.P.E.X.","score":0.7730885744094849}'
`{"plot":"Agent J travels in time to M.I.B.'s early days in 1969 to stop an alien from assassinating his friend Agent K and changing history.","title":"Men in Black 3","score":0.7712380886077881}`
'{"plot":"Bound by a shared destiny, a teen bursting with scientific curiosity and a former boy-genius inventor embark on a mission to unearth the secrets of a place somewhere in time and space that exists in their collective memory.","title":"Tomorrowland","score":0.7669923901557922}'
'{"plot":"With the help of his uncle, a man travels to the future to try and bring his girlfriend back to life.","title":"Love Story 2050","score":0.7649372816085815}'
'{"plot":"A dimension-traveling wizard gets stuck in the 21st century because cell-phone radiation interferes with his magic. With his home world on the brink of war, he seeks help from a jaded ...","title":"The Portal","score":0.7640786170959473}'

```

</Tab>

<Tab name="PHP">

### Construct your vector search query.

This query searches for documents that include text in the `plot` field that is semantically related to the term "time travel".

- Create a file named `atlas-vector-search-quick-start.php`.

- Copy and paste the following sample query into the `atlas-vector-search-quick-start.php` file:

  ```php
  <?php

  require 'vendor/autoload.php';

  // Replace the placeholder with your Atlas connection string
  $uri = '<connection-string>';

  try {

      // Connect to your Atlas cluster
      $client = new MongoDB\Client($uri);

      // Set the namespace
      $database = $client->selectDatabase('sample_mflix');
      $collection = $database->selectCollection('embedded_movies');

      // Define the pipeline with $vectorSearch query options
      $pipeline = [
          [
              '$vectorSearch' => [
                  'queryVector' => [
                      -0.0016261312, -0.*********, ...
                  ],
                  'index' => 'vector_index',
                  'path' => 'plot_embedding',
                  'numCandidates' => 150,
                  'limit' => 10
              ]
          ],
          [
              '$project' => [
                  '_id' => 0,
                  'plot' => 1,
                  'title' => 1,
                  'score' => [
                      '$meta' => 'vectorSearchScore'
                  ]
              ]
          ]
      ];

      // Run the pipeline
      $result = $collection->aggregate($pipeline);

      // Print results
      foreach ($result as $document) {
          echo json_encode($document) . "\n";
      }

  } catch (MongoDB\Driver\Exception\Exception $e) {
      echo "Error: " . $e->getMessage() . "\n";
  }

  ```

This query uses the `$vectorSearch` stage to:

- Compare vector embeddings of the search term against vector embeddings of movie plots in the `plot_embedding` field of the `sample_mflix.embedded_movies` collection.

- Consider up to the 150 most similar movie plots and return the top 10 results.

It uses the `$project` stage to:

- Only include the movie `plot` and `title` fields in the results.

- Add a `score` field to show the relevance of each result to the search term.

To learn more about this pipeline stage, see [Run Vector Search Queries](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-stage/#std-label-return-vector-search-results).

### Specify the `<connection-string>`.

<Tabs>

<Tab name="Atlas Cluster">

Your connection string should use the following format:

```
mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
```

Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

</Tab>

<Tab name="Local Deployment">

Your connection string should use the following format:

```
mongodb://localhost:<port-number>/?directConnection=true
```

</Tab>

</Tabs>

### Run your query.

Run the following command to query your collection:

```bash
php atlas-vector-search-quick-start.php
```

```js
'{"plot":"A reporter, learning of time travelers visiting 20th century disasters, tries to change the history they know by averting upcoming disasters.","title":"Thrill Seekers","score":0.7892671227455139}'
'{"plot":"At the age of 21, Tim discovers he can travel in time and change what happens and has happened in his own life. His decision to make his world a better place by getting a girlfriend turns out not to be as easy as you might think.","title":"About Time","score":0.7843604683876038}'
'{"plot":"Hoping to alter the events of the past, a 19th century inventor instead travels 800,000 years into the future, where he finds humankind divided into two warring races.","title":"The Time Machine","score":0.7801066637039185}'
`{"plot":"After using his mother's newly built time machine, Dolf gets stuck involuntary in the year 1212. He ends up in a children's crusade where he confronts his new friends with modern techniques...","title":"Crusade in Jeans","score":0.7789170742034912}`
'{"plot":"An officer for a security agency that regulates time travel, must fend for his life against a shady politician who has a tie to his past.","title":"Timecop","score":0.7771612405776978}'
'{"plot":"A time-travel experiment in which a robot probe is sent from the year 2073 to the year 1973 goes terribly wrong thrusting one of the project scientists, a man named Nicholas Sinclair into a...","title":"A.P.E.X.","score":0.7730885744094849}'
`{"plot":"Agent J travels in time to M.I.B.'s early days in 1969 to stop an alien from assassinating his friend Agent K and changing history.","title":"Men in Black 3","score":0.7712380886077881}`
'{"plot":"Bound by a shared destiny, a teen bursting with scientific curiosity and a former boy-genius inventor embark on a mission to unearth the secrets of a place somewhere in time and space that exists in their collective memory.","title":"Tomorrowland","score":0.7669923901557922}'
'{"plot":"With the help of his uncle, a man travels to the future to try and bring his girlfriend back to life.","title":"Love Story 2050","score":0.7649372816085815}'
'{"plot":"A dimension-traveling wizard gets stuck in the 21st century because cell-phone radiation interferes with his magic. With his home world on the brink of war, he seeks help from a jaded ...","title":"The Portal","score":0.7640786170959473}'

```

</Tab>

<Tab name="Python">

### Construct your vector search query.

This query searches for documents that include text in the `plot` field that is semantically related to the term "time travel".

- Create a file named `atlas-vector-search-quick-start.py`.

- Copy and paste the following sample query into the `atlas-vector-search-quick-start.py` file:

  ```python
  import pymongo

  # connect to your Atlas cluster
  client = pymongo.MongoClient("<connection-string>")

  # define pipeline
  pipeline = [
    {
      '$vectorSearch': {
        'index': 'vector_index',
        'path': 'plot_embedding',
        'queryVector': [-0.0016261312, -0.*********, ...],
        'numCandidates': 150,
        'limit': 10
      }
    }, {
      '$project': {
        '_id': 0,
        'plot': 1,
        'title': 1,
        'score': {
          '$meta': 'vectorSearchScore'
        }
      }
    }
  ]

  # run pipeline
  result = client["sample_mflix"]["embedded_movies"].aggregate(pipeline)

  # print results
  for i in result:
      print(i)

  ```

This query uses the `$vectorSearch` stage to:

- Compare vector embeddings of the search term against vector embeddings of movie plots in the `plot_embedding` field of the `sample_mflix.embedded_movies` collection.

- Consider up to the 150 most similar movie plots and return the top 10 results.

It uses the `$project` stage to:

- Only include the movie `plot` and `title` fields in the results.

- Add a `score` field to show the relevance of each result to the search term.

To learn more about this pipeline stage, see [Run Vector Search Queries](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-stage/#std-label-return-vector-search-results).

### Specify the `<connection-string>`.

<Tabs>

<Tab name="Atlas Cluster">

Your connection string should use the following format:

```
mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
```

Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

</Tab>

<Tab name="Local Deployment">

Your connection string should use the following format:

```
mongodb://localhost:<port-number>/?directConnection=true
```

</Tab>

</Tabs>

### Run your query.

Run the following command to query your collection:

```bash
python atlas-vector-search-quick-start.py
```

```js
{'plot': 'A reporter, learning of time travelers visiting 20th century disasters, tries to change the history they know by averting upcoming disasters.', 'title': 'Thrill Seekers', 'score': 0.7892671227455139}
{'plot': 'At the age of 21, Tim discovers he can travel in time and change what happens and has happened in his own life. His decision to make his world a better place by getting a girlfriend turns out not to be as easy as you might think.', 'title': 'About Time', 'score': 0.7843604683876038}
{'plot': 'Hoping to alter the events of the past, a 19th century inventor instead travels 800,000 years into the future, where he finds humankind divided into two warring races.', 'title': 'The Time Machine', 'score': 0.7801066637039185}
{'plot': "After using his mother's newly built time machine, Dolf gets stuck involuntary in the year 1212. He ends up in a children's crusade where he confronts his new friends with modern techniques...", 'title': 'Crusade in Jeans', 'score': 0.7789170742034912}
{'plot': 'An officer for a security agency that regulates time travel, must fend for his life against a shady politician who has a tie to his past.', 'title': 'Timecop', 'score': 0.7771612405776978}
{'plot': 'A time-travel experiment in which a robot probe is sent from the year 2073 to the year 1973 goes terribly wrong thrusting one of the project scientists, a man named Nicholas Sinclair into a...', 'title': 'A.P.E.X.', 'score': 0.7730885744094849}
{'plot': "Agent J travels in time to M.I.B.'s early days in 1969 to stop an alien from assassinating his friend Agent K and changing history.", 'title': 'Men in Black 3', 'score': 0.7712380886077881}
{'plot': 'Bound by a shared destiny, a teen bursting with scientific curiosity and a former boy-genius inventor embark on a mission to unearth the secrets of a place somewhere in time and space that exists in their collective memory.', 'title': 'Tomorrowland', 'score': 0.7669923901557922}
{'plot': 'With the help of his uncle, a man travels to the future to try and bring his girlfriend back to life.', 'title': 'Love Story 2050', 'score': 0.7649372816085815}
{'plot': 'A dimension-traveling wizard gets stuck in the 21st century because cell-phone radiation interferes with his magic. With his home world on the brink of war, he seeks help from a jaded ...', 'title': 'The Portal', 'score': 0.7640786170959473}

```

</Tab>

<Tab name="Ruby">

### Install the MongoDB Ruby Driver.

- If you don't already have a `Gemfile` for your project, run the following command to generate one:

  ```sh
  bundle init
  ```

- Add the `mongo` gem to your `Gemfile`:

  ```sh
  gem "mongo"
  ```

- Run the following command to install the dependency:

  ```sh
  bundle install
  ```

This installs the latest version of the Ruby driver. For alternate installation instructions and version compatibility, see the [MongoDB Ruby Driver documentation](https://www.mongodb.com/docs/ruby-driver/current/installation/#std-label-installation).

### Construct your vector search query.

This query searches for documents that include text in the `plot` field that is semantically related to the term "time travel".

- Create a file named `atlas_vector_search_quick_start.rb` .

- Copy and paste the following sample query into the `atlas_vector_search_quick_start.rb` file:

  ```ruby
  require 'mongo'
  require 'json'

  # Replace the placeholder with your Atlas connection string
  uri = "<connection-string>"

  begin
    # Connect to the MongoDB client
    client = Mongo::Client.new(uri, database: 'sample_mflix')

    # Set the namespace
    db = client.database
    collection = db[:embedded_movies]

    # Define the pipeline with $vectorSearch query options
    pipeline = [
      {
        '$vectorSearch' => {
          'queryVector' => [ -0.0016261312, -0.*********, ... ],
          'index' => 'vector_index',
          'path' => 'plot_embedding',
          'numCandidates' => 150,
          'limit' => 10
        }
      },
      {
        '$project' => {
          '_id' => 0,
          'plot' => 1,
          'title' => 1,
          'score' => { '$meta' => 'vectorSearchScore' }
        }
      }
    ]

    # Run the pipeline
    results = collection.aggregate(pipeline)

    # Print results
    results.each do |document|
      puts JSON(document)
    end

  rescue Mongo::Error::NoServerAvailable => e
    puts "Failed to connect to MongoDB: #{e.message}"

  rescue Mongo::Error::OperationFailure => e
    puts "Failed to run aggregation: #{e.message}"

  rescue Mongo::Error => e
    puts "A MongoDB error occurred: #{e.message}"

  rescue StandardError => e
    puts "A general error occurred: #{e.message}"

  ensure
    client&.close
    puts "MongoDB client closed."
  end

  ```

This query uses the `$vectorSearch` stage to:

- Compare vector embeddings of the search term against vector embeddings of movie plots in the `plot_embedding` field of the `sample_mflix.embedded_movies` collection.

- Consider up to the 150 most similar movie plots and return the top 10 results.

It uses the `$project` stage to:

- Only include the movie `plot` and `title` fields in the results.

- Add a `score` field to show the relevance of each result to the search term.

To learn more about this pipeline stage, see [Run Vector Search Queries](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-stage/#std-label-return-vector-search-results).

### Specify the `<connection-string>`.

<Tabs>

<Tab name="Atlas Cluster">

Your connection string should use the following format:

```
mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
```

Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

</Tab>

<Tab name="Local Deployment">

Your connection string should use the following format:

```
mongodb://localhost:<port-number>/?directConnection=true
```

</Tab>

</Tabs>

### Run your query.

Run the following command to query your collection:

```bash
bundle exec ruby atlas_vector_search_quick_start.rb
```

```js
'{"plot":"A reporter, learning of time travelers visiting 20th century disasters, tries to change the history they know by averting upcoming disasters.","title":"Thrill Seekers","score":0.7892671227455139}'
'{"plot":"At the age of 21, Tim discovers he can travel in time and change what happens and has happened in his own life. His decision to make his world a better place by getting a girlfriend turns out not to be as easy as you might think.","title":"About Time","score":0.7843604683876038}'
'{"plot":"Hoping to alter the events of the past, a 19th century inventor instead travels 800,000 years into the future, where he finds humankind divided into two warring races.","title":"The Time Machine","score":0.7801066637039185}'
`{"plot":"After using his mother's newly built time machine, Dolf gets stuck involuntary in the year 1212. He ends up in a children's crusade where he confronts his new friends with modern techniques...","title":"Crusade in Jeans","score":0.7789170742034912}`
'{"plot":"An officer for a security agency that regulates time travel, must fend for his life against a shady politician who has a tie to his past.","title":"Timecop","score":0.7771612405776978}'
'{"plot":"A time-travel experiment in which a robot probe is sent from the year 2073 to the year 1973 goes terribly wrong thrusting one of the project scientists, a man named Nicholas Sinclair into a...","title":"A.P.E.X.","score":0.7730885744094849}'
`{"plot":"Agent J travels in time to M.I.B.'s early days in 1969 to stop an alien from assassinating his friend Agent K and changing history.","title":"Men in Black 3","score":0.7712380886077881}`
'{"plot":"Bound by a shared destiny, a teen bursting with scientific curiosity and a former boy-genius inventor embark on a mission to unearth the secrets of a place somewhere in time and space that exists in their collective memory.","title":"Tomorrowland","score":0.7669923901557922}'
'{"plot":"With the help of his uncle, a man travels to the future to try and bring his girlfriend back to life.","title":"Love Story 2050","score":0.7649372816085815}'
'{"plot":"A dimension-traveling wizard gets stuck in the 21st century because cell-phone radiation interferes with his magic. With his home world on the brink of war, he seeks help from a jaded ...","title":"The Portal","score":0.7640786170959473}'

```

</Tab>

<Tab name="Rust">

### Construct your vector search query.

This query searches for documents that include text in the `plot` field that is semantically related to the term "time travel".

- Create a file named `basic_query.rs`.

- Copy and paste the following sample query into the `basic_query.rs` file:

  <Tabs>

  <Tab name="">

  ```rust
  #![recursion_limit = "2560"]
  use mongodb::{
      bson::{Document, doc},
      Client
  };
  use futures::TryStreamExt;

  #[tokio::main]
  async fn basic_query() -> mongodb::error::Result<()> {
      // Replace the placeholder with your Atlas connection string
      let client = Client::with_uri_str("<connection-string>").await?;

      let pipeline  = vec! [
          doc! {
              "$vectorSearch": doc! {
              "queryVector": [ -0.0016261312, -0.*********, ...
              ],
              "path": "plot_embedding",
              "numCandidates": 150,
              "index": "vector_index",
              "limit": 10
          }
          },
          doc! {
              "$project": doc! {
                  "_id": 0,
                  "plot": 1,
                  "title": 1,
                  "score": doc! { "$meta": "vectorSearchScore"
                  }
              }
          }
      ];
      let coll = client.database("sample_mflix").collection::<Document>("embedded_movies");
      let mut results = coll.aggregate(pipeline).await?;
      while let Some(result) = results.try_next().await? {
          println!("{}", result);
      }
      Ok(())
  }
  ```

  </Tab>

  <Tab name="">

  ```rust
  #![recursion_limit = "2560"]
  use mongodb::{
      bson::{Document, doc},
      sync::Client
  };

  fn basic_query() -> mongodb::error::Result<()> {
      // Replace the placeholder with your Atlas connection string
      let client = Client::with_uri_str("<connection-string>")?;

      let pipeline  = vec! [
          doc! {
              "$vectorSearch": doc! {
              "queryVector": [ -0.0016261312, -0.*********, ...
              ],
              "path": "plot_embedding",
              "numCandidates": 150,
              "index": "vector_index",
              "limit": 10
          }
          },
          doc! {
              "$project": doc! {
                  "_id": 0,
                  "plot": 1,
                  "title": 1,
                  "score": doc! { "$meta": "vectorSearchScore"
                  }
              }
          }
      ];
      let coll = client.database("sample_mflix").collection::<Document>("embedded_movies");
      let mut cursor = coll.aggregate(pipeline).run()?;

      while cursor.advance()? {
          println!("{}", cursor.deserialize_current()?);
      }

      Ok(())
  }

  ```

  </Tab>

  </Tabs>

This query uses the `$vectorSearch` stage to:

- Compare vector embeddings of the search term against vector embeddings of movie plots in the `plot_embedding` field of the `sample_mflix.embedded_movies` collection.

- Consider up to the 150 most similar movie plots and return the top 10 results.

It uses the `$project` stage to:

- Only include the movie `plot` and `title` fields in the results.

- Add a `score` field to show the relevance of each result to the search term.

To learn more about this pipeline stage, see [Run Vector Search Queries](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-stage/#std-label-return-vector-search-results).

### Specify the `<connection-string>`.

<Tabs>

<Tab name="Atlas Cluster">

Your connection string should use the following format:

```
mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
```

Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

</Tab>

<Tab name="Local Deployment">

Your connection string should use the following format:

```
mongodb://localhost:<port-number>/?directConnection=true
```

</Tab>

</Tabs>

### Run your query.

- Call the function from your `main.rs`.

  ```rust
  fn main() {
     basic_query().unwrap();
  }
  ```

- Run the file in your IDE, or execute a command from the command line to run the code.

  ```bash
  cargo run
  ```

  ```js
  {'plot': 'A reporter, learning of time travelers visiting 20th century disasters, tries to change the history they know by averting upcoming disasters.', 'title': 'Thrill Seekers', 'score': 0.7892671227455139}
  {'plot': 'At the age of 21, Tim discovers he can travel in time and change what happens and has happened in his own life. His decision to make his world a better place by getting a girlfriend turns out not to be as easy as you might think.', 'title': 'About Time', 'score': 0.7843604683876038}
  {'plot': 'Hoping to alter the events of the past, a 19th century inventor instead travels 800,000 years into the future, where he finds humankind divided into two warring races.', 'title': 'The Time Machine', 'score': 0.7801066637039185}
  {'plot': "After using his mother's newly built time machine, Dolf gets stuck involuntary in the year 1212. He ends up in a children's crusade where he confronts his new friends with modern techniques...", 'title': 'Crusade in Jeans', 'score': 0.7789170742034912}
  {'plot': 'An officer for a security agency that regulates time travel, must fend for his life against a shady politician who has a tie to his past.', 'title': 'Timecop', 'score': 0.7771612405776978}
  {'plot': 'A time-travel experiment in which a robot probe is sent from the year 2073 to the year 1973 goes terribly wrong thrusting one of the project scientists, a man named Nicholas Sinclair into a...', 'title': 'A.P.E.X.', 'score': 0.7730885744094849}
  {'plot': "Agent J travels in time to M.I.B.'s early days in 1969 to stop an alien from assassinating his friend Agent K and changing history.", 'title': 'Men in Black 3', 'score': 0.7712380886077881}
  {'plot': 'Bound by a shared destiny, a teen bursting with scientific curiosity and a former boy-genius inventor embark on a mission to unearth the secrets of a place somewhere in time and space that exists in their collective memory.', 'title': 'Tomorrowland', 'score': 0.7669923901557922}
  {'plot': 'With the help of his uncle, a man travels to the future to try and bring his girlfriend back to life.', 'title': 'Love Story 2050', 'score': 0.7649372816085815}
  {'plot': 'A dimension-traveling wizard gets stuck in the 21st century because cell-phone radiation interferes with his magic. With his home world on the brink of war, he seeks help from a jaded ...', 'title': 'The Portal', 'score': 0.7640786170959473}

  ```

</Tab>

<Tab name="Scala">

### Construct your vector search query.

This query searches for documents that include text in the `plot` field that is semantically related to the term "time travel".

Create a file named `VectorQuery.scala`. Copy and paste the following code into the file.

```scala
import org.mongodb.scala._
import org.mongodb.scala.model._
import org.mongodb.scala.model.search.SearchPath.fieldPath
import org.mongodb.scala.model.search.VectorSearchOptions

class VectorQuery {
  def performVectorQuery() = {
    val collection =
      MongoClient("<connection-string>")
        .getDatabase("sample_mflix")
        .getCollection("embedded_movies")

    val options: VectorSearchOptions = VectorSearchOptions.approximateVectorSearchOptions(150)

    collection.aggregate(Seq(
      Aggregates.vectorSearch(fieldPath("plot_embedding"),
        List(-0.0016261312, -0.*********, ...),
        "vector_index",
        10,
        options
      ),
      Aggregates.project(
        Projections.fields(
          Projections.excludeId(),
          Projections.include("plot"),
          Projections.include("title"),
          Projections.meta(
            "score", "vectorSearchScore"
          )
        )
      )
    )).foreach { doc => println(doc.toBsonDocument()) }
  }
}
```

This query uses the `$vectorSearch` stage to:

- Compare vector embeddings of the search term against vector embeddings of movie plots in the `plot_embedding` field of the `sample_mflix.embedded_movies` collection.

- Consider up to the 150 most similar movie plots and return the top 10 results.

It uses the `$project` stage to:

- Only include the movie `plot` and `title` fields in the results.

- Add a `score` field to show the relevance of each result to the search term.

To learn more about this pipeline stage, see [Run Vector Search Queries](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-stage/#std-label-return-vector-search-results).

### Specify the `<connection-string>`.

<Tabs>

<Tab name="Atlas Cluster">

Your connection string should use the following format:

```
mongodb+srv://<db_username>:<db_password>@<clusterName>.<hostname>.mongodb.net
```

Ensure that your connection string includes your database user's credentials. To learn more about finding your connection string, see [Connect via Drivers](https://mongodb.com/docs/atlas/driver-connection/#std-label-connect-via-driver).

</Tab>

<Tab name="Local Deployment">

Your connection string should use the following format:

```
mongodb://localhost:<port-number>/?directConnection=true
```

</Tab>

</Tabs>

### Update your `Main.scala` file.

Create a class instance and call the function in your project's `Main.scala` file.

```scala
object Main extends App {
   private val queryInstance = new VectorQuery
   queryInstance.performVectorQuery()
}
```

### Run your query.

```bash
sbt:quick-start> ~run
```

```js
'{"plot":"A reporter, learning of time travelers visiting 20th century disasters, tries to change the history they know by averting upcoming disasters.","title":"Thrill Seekers","score":0.7892671227455139}'
'{"plot":"At the age of 21, Tim discovers he can travel in time and change what happens and has happened in his own life. His decision to make his world a better place by getting a girlfriend turns out not to be as easy as you might think.","title":"About Time","score":0.7843604683876038}'
'{"plot":"Hoping to alter the events of the past, a 19th century inventor instead travels 800,000 years into the future, where he finds humankind divided into two warring races.","title":"The Time Machine","score":0.7801066637039185}'
`{"plot":"After using his mother's newly built time machine, Dolf gets stuck involuntary in the year 1212. He ends up in a children's crusade where he confronts his new friends with modern techniques...","title":"Crusade in Jeans","score":0.7789170742034912}`
'{"plot":"An officer for a security agency that regulates time travel, must fend for his life against a shady politician who has a tie to his past.","title":"Timecop","score":0.7771612405776978}'
'{"plot":"A time-travel experiment in which a robot probe is sent from the year 2073 to the year 1973 goes terribly wrong thrusting one of the project scientists, a man named Nicholas Sinclair into a...","title":"A.P.E.X.","score":0.7730885744094849}'
`{"plot":"Agent J travels in time to M.I.B.'s early days in 1969 to stop an alien from assassinating his friend Agent K and changing history.","title":"Men in Black 3","score":0.7712380886077881}`
'{"plot":"Bound by a shared destiny, a teen bursting with scientific curiosity and a former boy-genius inventor embark on a mission to unearth the secrets of a place somewhere in time and space that exists in their collective memory.","title":"Tomorrowland","score":0.7669923901557922}'
'{"plot":"With the help of his uncle, a man travels to the future to try and bring his girlfriend back to life.","title":"Love Story 2050","score":0.7649372816085815}'
'{"plot":"A dimension-traveling wizard gets stuck in the 21st century because cell-phone radiation interferes with his magic. With his home world on the brink of war, he seeks help from a jaded ...","title":"The Portal","score":0.7640786170959473}'

```

</Tab>

</Tabs>

<div id="std-label-avs-qs-learning-summary"></div>

## Learning Summary

This quick start focused on retrieving documents from your Atlas cluster that contain text that is semantically related to a provided query. However, you can create a vector search index on embeddings that represent any type of data that you might write to an Atlas cluster, such as images or videos.

<div id="std-label-vector-search-quickstart-sample-data"></div>

### Sample Data

This quick start uses the `sample_mflix.embedded_movies` collection which contains details about movies. In each document in the collection, the `plot_embedding` field contains a vector embedding that represents the string in the `plot` field. For more information on the schema of the documents in the collection, see [Sample Mflix Dataset](https://mongodb.com/docs/atlas/sample-data/sample-mflix/#std-label-mflix-embedded_movies).

By storing your source data and its corresponding vector embeddings in the same document, you can leverage both fields for complex queries or [hybrid search](https://mongodb.com/docs/atlas/atlas-vector-search/tutorials/reciprocal-rank-fusion/#std-label-avs-reciprocal-rank-fusion). You can even store vector embeddings generated from different [embedding models](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-label-avs-key-concepts) in the same document to streamline your workflow as you test the performance of different vector embedding models for your specific use case.

### Vector Embeddings

The vector embeddings in the `sample_mflix.embedded_movies` collection and in the example query were created using the OpenAI `text-embedding-ada-002` embedding model. Your choice of embedding model informs the vector dimensions and vector similarity function you use in your vector search index. You can use any [embedding model](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-overview/#std-label-avs-key-concepts) you like, and it is worth experimenting with different models as accuracy can vary from model to model depending on your specific use case.

To learn how to create vector embeddings of your own data, see [How to Create Vector Embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/create-embeddings/#std-label-create-vector-embeddings).

<div id="std-label-vector-search-quickstart-vector-index-definition"></div>

### Vector Index Definition

An [index](https://www.mongodb.com/docs/manual/indexes/) is a data structure that holds a subset of data from a collection's documents that improves database performance for specific queries. A [vector search index](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-type/#std-label-avs-types-vector-search) points to the fields that contain your vector embeddings and includes the dimensions of your vectors as well as the function used to measure similarity between vectors of queries and vectors stored in the database.

Because the `text-embedding-ada-002` embedding model used in this quick start converts data into vector embeddings with 1536 dimensions and supports the `cosine` function, this vector search index specifies the same number of vector dimensions and similarity function.

### Vector Search Query

The query you ran in this quick start is an [aggregation pipeline](https://www.mongodb.com/docs/manual/core/aggregation-pipeline), in which the `$vectorSearch` stage performs an [Approximate Nearest Neighbor (ANN)](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-stage/#std-label-vectorSearch-ann) search followed by a `$project` stage that refines the results. To see all the options for a vector search query, including using [Exact Nearest Neighbor (ENN)](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-stage/#std-label-vectorSearch-enn) or how to narrow the scope of your vector search with the `filter` option, see [Run Vector Search Queries](https://mongodb.com/docs/atlas/atlas-vector-search/vector-search-stage/#std-label-return-vector-search-results).

## Next Steps

- To learn how to create embeddings from data and load them into Atlas, see [Create Embeddings](https://mongodb.com/docs/atlas/atlas-vector-search/create-embeddings/#std-label-create-vector-embeddings).

- To learn how to implement retrieval-augmented generation (RAG), see [Retrieval-Augmented Generation (RAG) with Atlas Vector Search](https://mongodb.com/docs/atlas/atlas-vector-search/rag/#std-label-avs-rag).

- To integrate Atlas Vector Search with popular AI frameworks and services, see [Integrate Vector Search with AI Technologies](https://mongodb.com/docs/atlas/atlas-vector-search/ai-integrations/#std-label-ai-integrations).

- To build production ready AI chatbots using Atlas Vector Search, see the [MongoDB Chatbot Framework](https://mongodb.github.io/chatbot/).

- To learn how implement RAG (Retrieval-Augmented Generation) without the need for API (Application Programming Interface) keys or credits, see [Build a Local RAG Implementation with Atlas Vector Search](https://mongodb.com/docs/atlas/atlas-vector-search/tutorials/local-rag/#std-label-local-rag).